package com.wzsec.modules.sdd.api.service.mapper;

import com.wzsec.modules.sdd.api.domain.ApiUrlmapping;
import com.wzsec.modules.sdd.api.service.dto.ApiUrlmappingDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiUrlmappingMapperImpl implements ApiUrlmappingMapper {

    @Override
    public ApiUrlmapping toEntity(ApiUrlmappingDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUrlmapping apiUrlmapping = new ApiUrlmapping();

        apiUrlmapping.setId( dto.getId() );
        apiUrlmapping.setApiname( dto.getApiname() );
        apiUrlmapping.setProxyapi( dto.getProxyapi() );
        apiUrlmapping.setProtocol( dto.getProtocol() );
        apiUrlmapping.setMethod( dto.getMethod() );
        apiUrlmapping.setServerhost( dto.getServerhost() );
        apiUrlmapping.setServerport( dto.getServerport() );
        apiUrlmapping.setServerapi( dto.getServerapi() );
        apiUrlmapping.setIsmask( dto.getIsmask() );
        apiUrlmapping.setIsjson( dto.getIsjson() );
        apiUrlmapping.setSplit( dto.getSplit() );
        apiUrlmapping.setCreateuser( dto.getCreateuser() );
        apiUrlmapping.setCreatetime( dto.getCreatetime() );
        apiUrlmapping.setUpdateuser( dto.getUpdateuser() );
        apiUrlmapping.setUpdatetime( dto.getUpdatetime() );
        apiUrlmapping.setNote( dto.getNote() );
        apiUrlmapping.setSparefield1( dto.getSparefield1() );
        apiUrlmapping.setSparefield2( dto.getSparefield2() );
        apiUrlmapping.setSparefield3( dto.getSparefield3() );
        apiUrlmapping.setSparefield4( dto.getSparefield4() );
        apiUrlmapping.setSparefield5( dto.getSparefield5() );

        return apiUrlmapping;
    }

    @Override
    public ApiUrlmappingDto toDto(ApiUrlmapping entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUrlmappingDto apiUrlmappingDto = new ApiUrlmappingDto();

        apiUrlmappingDto.setId( entity.getId() );
        apiUrlmappingDto.setApiname( entity.getApiname() );
        apiUrlmappingDto.setProxyapi( entity.getProxyapi() );
        apiUrlmappingDto.setProtocol( entity.getProtocol() );
        apiUrlmappingDto.setMethod( entity.getMethod() );
        apiUrlmappingDto.setServerhost( entity.getServerhost() );
        apiUrlmappingDto.setServerport( entity.getServerport() );
        apiUrlmappingDto.setServerapi( entity.getServerapi() );
        apiUrlmappingDto.setIsmask( entity.getIsmask() );
        apiUrlmappingDto.setIsjson( entity.getIsjson() );
        apiUrlmappingDto.setSplit( entity.getSplit() );
        apiUrlmappingDto.setCreateuser( entity.getCreateuser() );
        apiUrlmappingDto.setCreatetime( entity.getCreatetime() );
        apiUrlmappingDto.setUpdateuser( entity.getUpdateuser() );
        apiUrlmappingDto.setUpdatetime( entity.getUpdatetime() );
        apiUrlmappingDto.setNote( entity.getNote() );
        apiUrlmappingDto.setSparefield1( entity.getSparefield1() );
        apiUrlmappingDto.setSparefield2( entity.getSparefield2() );
        apiUrlmappingDto.setSparefield3( entity.getSparefield3() );
        apiUrlmappingDto.setSparefield4( entity.getSparefield4() );
        apiUrlmappingDto.setSparefield5( entity.getSparefield5() );

        return apiUrlmappingDto;
    }

    @Override
    public List<ApiUrlmapping> toEntity(List<ApiUrlmappingDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUrlmapping> list = new ArrayList<ApiUrlmapping>( dtoList.size() );
        for ( ApiUrlmappingDto apiUrlmappingDto : dtoList ) {
            list.add( toEntity( apiUrlmappingDto ) );
        }

        return list;
    }

    @Override
    public List<ApiUrlmappingDto> toDto(List<ApiUrlmapping> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUrlmappingDto> list = new ArrayList<ApiUrlmappingDto>( entityList.size() );
        for ( ApiUrlmapping apiUrlmapping : entityList ) {
            list.add( toDto( apiUrlmapping ) );
        }

        return list;
    }
}
