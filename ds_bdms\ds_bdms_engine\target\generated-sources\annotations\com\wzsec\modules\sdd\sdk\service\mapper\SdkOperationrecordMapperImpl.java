package com.wzsec.modules.sdd.sdk.service.mapper;

import com.wzsec.modules.sdd.sdk.domain.SdkOperationrecord;
import com.wzsec.modules.sdd.sdk.service.dto.SdkOperationrecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class SdkOperationrecordMapperImpl implements SdkOperationrecordMapper {

    @Override
    public SdkOperationrecord toEntity(SdkOperationrecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        SdkOperationrecord sdkOperationrecord = new SdkOperationrecord();

        sdkOperationrecord.setId( dto.getId() );
        sdkOperationrecord.setSdkid( dto.getSdkid() );
        sdkOperationrecord.setSdkname( dto.getSdkname() );
        sdkOperationrecord.setVersion( dto.getVersion() );
        sdkOperationrecord.setApplysystemname( dto.getApplysystemname() );
        sdkOperationrecord.setObjecttype( dto.getObjecttype() );
        sdkOperationrecord.setObjectname( dto.getObjectname() );
        sdkOperationrecord.setOperation( dto.getOperation() );
        sdkOperationrecord.setOperationtime( dto.getOperationtime() );
        sdkOperationrecord.setSparefield1( dto.getSparefield1() );
        sdkOperationrecord.setSparefield2( dto.getSparefield2() );
        sdkOperationrecord.setSparefield3( dto.getSparefield3() );
        sdkOperationrecord.setSparefield4( dto.getSparefield4() );

        return sdkOperationrecord;
    }

    @Override
    public SdkOperationrecordDto toDto(SdkOperationrecord entity) {
        if ( entity == null ) {
            return null;
        }

        SdkOperationrecordDto sdkOperationrecordDto = new SdkOperationrecordDto();

        sdkOperationrecordDto.setId( entity.getId() );
        sdkOperationrecordDto.setSdkid( entity.getSdkid() );
        sdkOperationrecordDto.setSdkname( entity.getSdkname() );
        sdkOperationrecordDto.setVersion( entity.getVersion() );
        sdkOperationrecordDto.setApplysystemname( entity.getApplysystemname() );
        sdkOperationrecordDto.setObjecttype( entity.getObjecttype() );
        sdkOperationrecordDto.setObjectname( entity.getObjectname() );
        sdkOperationrecordDto.setOperation( entity.getOperation() );
        sdkOperationrecordDto.setOperationtime( entity.getOperationtime() );
        sdkOperationrecordDto.setSparefield1( entity.getSparefield1() );
        sdkOperationrecordDto.setSparefield2( entity.getSparefield2() );
        sdkOperationrecordDto.setSparefield3( entity.getSparefield3() );
        sdkOperationrecordDto.setSparefield4( entity.getSparefield4() );

        return sdkOperationrecordDto;
    }

    @Override
    public List<SdkOperationrecord> toEntity(List<SdkOperationrecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SdkOperationrecord> list = new ArrayList<SdkOperationrecord>( dtoList.size() );
        for ( SdkOperationrecordDto sdkOperationrecordDto : dtoList ) {
            list.add( toEntity( sdkOperationrecordDto ) );
        }

        return list;
    }

    @Override
    public List<SdkOperationrecordDto> toDto(List<SdkOperationrecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SdkOperationrecordDto> list = new ArrayList<SdkOperationrecordDto>( entityList.size() );
        for ( SdkOperationrecord sdkOperationrecord : entityList ) {
            list.add( toDto( sdkOperationrecord ) );
        }

        return list;
    }
}
