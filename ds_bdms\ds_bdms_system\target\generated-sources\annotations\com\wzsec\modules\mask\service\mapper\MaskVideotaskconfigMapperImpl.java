package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskVideotaskconfig;
import com.wzsec.modules.mask.service.dto.MaskVideotaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskVideotaskconfigMapperImpl implements MaskVideotaskconfigMapper {

    @Override
    public MaskVideotaskconfig toEntity(MaskVideotaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskVideotaskconfig maskVideotaskconfig = new MaskVideotaskconfig();

        maskVideotaskconfig.setId( dto.getId() );
        maskVideotaskconfig.setTaskname( dto.getTaskname() );
        maskVideotaskconfig.setInputdirectory( dto.getInputdirectory() );
        maskVideotaskconfig.setInputfileformat( dto.getInputfileformat() );
        maskVideotaskconfig.setComputeresources( dto.getComputeresources() );
        maskVideotaskconfig.setMaskobject( dto.getMaskobject() );
        maskVideotaskconfig.setState( dto.getState() );
        maskVideotaskconfig.setOutputfileformat( dto.getOutputfileformat() );
        maskVideotaskconfig.setOutputdirectory( dto.getOutputdirectory() );
        maskVideotaskconfig.setExecutionstate( dto.getExecutionstate() );
        maskVideotaskconfig.setCreateuser( dto.getCreateuser() );
        maskVideotaskconfig.setCreatetime( dto.getCreatetime() );
        maskVideotaskconfig.setUpdateuser( dto.getUpdateuser() );
        maskVideotaskconfig.setUpdatetime( dto.getUpdatetime() );
        maskVideotaskconfig.setRemark( dto.getRemark() );
        maskVideotaskconfig.setSparefield1( dto.getSparefield1() );
        maskVideotaskconfig.setSparefield2( dto.getSparefield2() );
        maskVideotaskconfig.setSparefield3( dto.getSparefield3() );
        maskVideotaskconfig.setSparefield4( dto.getSparefield4() );
        maskVideotaskconfig.setSparefield5( dto.getSparefield5() );

        return maskVideotaskconfig;
    }

    @Override
    public MaskVideotaskconfigDto toDto(MaskVideotaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        MaskVideotaskconfigDto maskVideotaskconfigDto = new MaskVideotaskconfigDto();

        maskVideotaskconfigDto.setId( entity.getId() );
        maskVideotaskconfigDto.setTaskname( entity.getTaskname() );
        maskVideotaskconfigDto.setInputdirectory( entity.getInputdirectory() );
        maskVideotaskconfigDto.setInputfileformat( entity.getInputfileformat() );
        maskVideotaskconfigDto.setComputeresources( entity.getComputeresources() );
        maskVideotaskconfigDto.setMaskobject( entity.getMaskobject() );
        maskVideotaskconfigDto.setState( entity.getState() );
        maskVideotaskconfigDto.setOutputfileformat( entity.getOutputfileformat() );
        maskVideotaskconfigDto.setOutputdirectory( entity.getOutputdirectory() );
        maskVideotaskconfigDto.setExecutionstate( entity.getExecutionstate() );
        maskVideotaskconfigDto.setCreateuser( entity.getCreateuser() );
        maskVideotaskconfigDto.setCreatetime( entity.getCreatetime() );
        maskVideotaskconfigDto.setUpdateuser( entity.getUpdateuser() );
        maskVideotaskconfigDto.setUpdatetime( entity.getUpdatetime() );
        maskVideotaskconfigDto.setRemark( entity.getRemark() );
        maskVideotaskconfigDto.setSparefield1( entity.getSparefield1() );
        maskVideotaskconfigDto.setSparefield2( entity.getSparefield2() );
        maskVideotaskconfigDto.setSparefield3( entity.getSparefield3() );
        maskVideotaskconfigDto.setSparefield4( entity.getSparefield4() );
        maskVideotaskconfigDto.setSparefield5( entity.getSparefield5() );

        return maskVideotaskconfigDto;
    }

    @Override
    public List<MaskVideotaskconfig> toEntity(List<MaskVideotaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskVideotaskconfig> list = new ArrayList<MaskVideotaskconfig>( dtoList.size() );
        for ( MaskVideotaskconfigDto maskVideotaskconfigDto : dtoList ) {
            list.add( toEntity( maskVideotaskconfigDto ) );
        }

        return list;
    }

    @Override
    public List<MaskVideotaskconfigDto> toDto(List<MaskVideotaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskVideotaskconfigDto> list = new ArrayList<MaskVideotaskconfigDto>( entityList.size() );
        for ( MaskVideotaskconfig maskVideotaskconfig : entityList ) {
            list.add( toDto( maskVideotaskconfig ) );
        }

        return list;
    }
}
