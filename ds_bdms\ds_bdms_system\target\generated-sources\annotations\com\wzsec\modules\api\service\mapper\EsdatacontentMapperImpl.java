package com.wzsec.modules.api.service.mapper;

import com.wzsec.modules.api.domain.Esdatacontent;
import com.wzsec.modules.api.service.dto.EsdatacontentDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class EsdatacontentMapperImpl implements EsdatacontentMapper {

    @Override
    public Esdatacontent toEntity(EsdatacontentDto dto) {
        if ( dto == null ) {
            return null;
        }

        Esdatacontent esdatacontent = new Esdatacontent();

        esdatacontent.setId( dto.getId() );
        esdatacontent.setDatacontent( dto.getDatacontent() );
        esdatacontent.setTime( dto.getTime() );
        esdatacontent.setDatasourcecontent( dto.getDatasourcecontent() );

        return esdatacontent;
    }

    @Override
    public EsdatacontentDto toDto(Esdatacontent entity) {
        if ( entity == null ) {
            return null;
        }

        EsdatacontentDto esdatacontentDto = new EsdatacontentDto();

        esdatacontentDto.setId( entity.getId() );
        esdatacontentDto.setDatacontent( entity.getDatacontent() );
        esdatacontentDto.setTime( entity.getTime() );
        esdatacontentDto.setDatasourcecontent( entity.getDatasourcecontent() );

        return esdatacontentDto;
    }

    @Override
    public List<Esdatacontent> toEntity(List<EsdatacontentDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Esdatacontent> list = new ArrayList<Esdatacontent>( dtoList.size() );
        for ( EsdatacontentDto esdatacontentDto : dtoList ) {
            list.add( toEntity( esdatacontentDto ) );
        }

        return list;
    }

    @Override
    public List<EsdatacontentDto> toDto(List<Esdatacontent> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EsdatacontentDto> list = new ArrayList<EsdatacontentDto>( entityList.size() );
        for ( Esdatacontent esdatacontent : entityList ) {
            list.add( toDto( esdatacontent ) );
        }

        return list;
    }
}
