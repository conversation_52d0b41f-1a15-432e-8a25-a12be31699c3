package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyField;
import com.wzsec.modules.mask.service.dto.MaskStrategyFieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyFieldMapperImpl implements MaskStrategyFieldMapper {

    @Override
    public MaskStrategyField toEntity(MaskStrategyFieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyField maskStrategyField = new MaskStrategyField();

        maskStrategyField.setId( dto.getId() );
        maskStrategyField.setStategytableid( dto.getStategytableid() );
        maskStrategyField.setTableid( dto.getTableid() );
        maskStrategyField.setExtractfield( dto.getExtractfield() );
        maskStrategyField.setTabename( dto.getTabename() );
        maskStrategyField.setTabcname( dto.getTabcname() );
        maskStrategyField.setDbname( dto.getDbname() );
        maskStrategyField.setFieldid( dto.getFieldid() );
        maskStrategyField.setFieldename( dto.getFieldename() );
        maskStrategyField.setFieldcname( dto.getFieldcname() );
        maskStrategyField.setFieldtype( dto.getFieldtype() );
        maskStrategyField.setSenLevel( dto.getSenLevel() );
        maskStrategyField.setRuleid( dto.getRuleid() );
        maskStrategyField.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyField.setParam( dto.getParam() );
        maskStrategyField.setSecretkey( dto.getSecretkey() );
        maskStrategyField.setSparefield1( dto.getSparefield1() );
        maskStrategyField.setSparefield2( dto.getSparefield2() );
        maskStrategyField.setSparefield3( dto.getSparefield3() );
        maskStrategyField.setSparefield4( dto.getSparefield4() );
        maskStrategyField.setSparefield5( dto.getSparefield5() );
        maskStrategyField.setSparefield6( dto.getSparefield6() );

        return maskStrategyField;
    }

    @Override
    public MaskStrategyFieldDto toDto(MaskStrategyField entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFieldDto maskStrategyFieldDto = new MaskStrategyFieldDto();

        maskStrategyFieldDto.setId( entity.getId() );
        maskStrategyFieldDto.setStategytableid( entity.getStategytableid() );
        maskStrategyFieldDto.setTableid( entity.getTableid() );
        maskStrategyFieldDto.setExtractfield( entity.getExtractfield() );
        maskStrategyFieldDto.setTabename( entity.getTabename() );
        maskStrategyFieldDto.setTabcname( entity.getTabcname() );
        maskStrategyFieldDto.setDbname( entity.getDbname() );
        maskStrategyFieldDto.setFieldid( entity.getFieldid() );
        maskStrategyFieldDto.setFieldename( entity.getFieldename() );
        maskStrategyFieldDto.setFieldcname( entity.getFieldcname() );
        maskStrategyFieldDto.setFieldtype( entity.getFieldtype() );
        maskStrategyFieldDto.setSenLevel( entity.getSenLevel() );
        maskStrategyFieldDto.setRuleid( entity.getRuleid() );
        maskStrategyFieldDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyFieldDto.setParam( entity.getParam() );
        maskStrategyFieldDto.setSecretkey( entity.getSecretkey() );
        maskStrategyFieldDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFieldDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFieldDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFieldDto.setSparefield4( entity.getSparefield4() );
        maskStrategyFieldDto.setSparefield5( entity.getSparefield5() );
        maskStrategyFieldDto.setSparefield6( entity.getSparefield6() );

        return maskStrategyFieldDto;
    }

    @Override
    public List<MaskStrategyField> toEntity(List<MaskStrategyFieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyField> list = new ArrayList<MaskStrategyField>( dtoList.size() );
        for ( MaskStrategyFieldDto maskStrategyFieldDto : dtoList ) {
            list.add( toEntity( maskStrategyFieldDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyFieldDto> toDto(List<MaskStrategyField> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFieldDto> list = new ArrayList<MaskStrategyFieldDto>( entityList.size() );
        for ( MaskStrategyField maskStrategyField : entityList ) {
            list.add( toDto( maskStrategyField ) );
        }

        return list;
    }
}
