package com.wzsec.modules.sdd.metadata.service.mapper;

import com.wzsec.modules.sdd.metadata.domain.Metadata;
import com.wzsec.modules.sdd.metadata.service.dto.MetadataDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MetadataMapperImpl implements MetadataMapper {

    @Override
    public Metadata toEntity(MetadataDto dto) {
        if ( dto == null ) {
            return null;
        }

        Metadata metadata = new Metadata();

        metadata.setId( dto.getId() );
        metadata.setTaskname( dto.getTaskname() );
        metadata.setFieldid( dto.getFieldid() );
        metadata.setFieldname( dto.getFieldname() );
        metadata.setFieldcname( dto.getFieldcname() );
        metadata.setFieldsname( dto.getFieldsname() );
        metadata.setFieldscname( dto.getFieldscname() );
        metadata.setExample( dto.getExample() );
        metadata.setFieldisvalid( dto.getFieldisvalid() );
        metadata.setTableid( dto.getTableid() );
        metadata.setTablename( dto.getTablename() );
        metadata.setTablecname( dto.getTablecname() );
        metadata.setTableisvalid( dto.getTableisvalid() );
        metadata.setDbid( dto.getDbid() );
        metadata.setDbname( dto.getDbname() );
        metadata.setDbisvalid( dto.getDbisvalid() );
        metadata.setSourcetype( dto.getSourcetype() );
        metadata.setColumnfamily( dto.getColumnfamily() );
        metadata.setCategory( dto.getCategory() );
        metadata.setLevel( dto.getLevel() );
        metadata.setAlgorithmid( dto.getAlgorithmid() );
        metadata.setSubjectdomain( dto.getSubjectdomain() );
        metadata.setStorepath( dto.getStorepath() );
        metadata.setVeracity( dto.getVeracity() );
        metadata.setConsistency( dto.getConsistency() );
        metadata.setGrundlagen( dto.getGrundlagen() );
        metadata.setEvaluationstate( dto.getEvaluationstate() );
        metadata.setEvaluationmethod( dto.getEvaluationmethod() );
        metadata.setNote( dto.getNote() );
        metadata.setCreateuser( dto.getCreateuser() );
        metadata.setCreatetime( dto.getCreatetime() );
        metadata.setUpdateuser( dto.getUpdateuser() );
        metadata.setUpdatetime( dto.getUpdatetime() );
        metadata.setSparefield1( dto.getSparefield1() );
        if ( dto.getSparefield2() != null ) {
            metadata.setSparefield2( Long.parseLong( dto.getSparefield2() ) );
        }
        metadata.setSparefield3( dto.getSparefield3() );
        metadata.setSparefield4( dto.getSparefield4() );
        metadata.setSparefield5( dto.getSparefield5() );

        return metadata;
    }

    @Override
    public MetadataDto toDto(Metadata entity) {
        if ( entity == null ) {
            return null;
        }

        MetadataDto metadataDto = new MetadataDto();

        metadataDto.setId( entity.getId() );
        metadataDto.setTaskname( entity.getTaskname() );
        metadataDto.setFieldid( entity.getFieldid() );
        metadataDto.setFieldname( entity.getFieldname() );
        metadataDto.setFieldcname( entity.getFieldcname() );
        metadataDto.setFieldsname( entity.getFieldsname() );
        metadataDto.setFieldscname( entity.getFieldscname() );
        metadataDto.setExample( entity.getExample() );
        metadataDto.setFieldisvalid( entity.getFieldisvalid() );
        metadataDto.setTableid( entity.getTableid() );
        metadataDto.setTablename( entity.getTablename() );
        metadataDto.setTablecname( entity.getTablecname() );
        metadataDto.setTableisvalid( entity.getTableisvalid() );
        metadataDto.setDbid( entity.getDbid() );
        metadataDto.setDbname( entity.getDbname() );
        metadataDto.setDbisvalid( entity.getDbisvalid() );
        metadataDto.setSourcetype( entity.getSourcetype() );
        metadataDto.setColumnfamily( entity.getColumnfamily() );
        metadataDto.setCategory( entity.getCategory() );
        metadataDto.setLevel( entity.getLevel() );
        metadataDto.setAlgorithmid( entity.getAlgorithmid() );
        metadataDto.setSubjectdomain( entity.getSubjectdomain() );
        metadataDto.setStorepath( entity.getStorepath() );
        metadataDto.setVeracity( entity.getVeracity() );
        metadataDto.setConsistency( entity.getConsistency() );
        metadataDto.setGrundlagen( entity.getGrundlagen() );
        metadataDto.setEvaluationstate( entity.getEvaluationstate() );
        metadataDto.setEvaluationmethod( entity.getEvaluationmethod() );
        metadataDto.setNote( entity.getNote() );
        metadataDto.setCreateuser( entity.getCreateuser() );
        metadataDto.setCreatetime( entity.getCreatetime() );
        metadataDto.setUpdateuser( entity.getUpdateuser() );
        metadataDto.setUpdatetime( entity.getUpdatetime() );
        metadataDto.setSparefield1( entity.getSparefield1() );
        if ( entity.getSparefield2() != null ) {
            metadataDto.setSparefield2( String.valueOf( entity.getSparefield2() ) );
        }
        metadataDto.setSparefield3( entity.getSparefield3() );
        metadataDto.setSparefield4( entity.getSparefield4() );
        metadataDto.setSparefield5( entity.getSparefield5() );

        return metadataDto;
    }

    @Override
    public List<Metadata> toEntity(List<MetadataDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Metadata> list = new ArrayList<Metadata>( dtoList.size() );
        for ( MetadataDto metadataDto : dtoList ) {
            list.add( toEntity( metadataDto ) );
        }

        return list;
    }

    @Override
    public List<MetadataDto> toDto(List<Metadata> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetadataDto> list = new ArrayList<MetadataDto>( entityList.size() );
        for ( Metadata metadata : entityList ) {
            list.add( toDto( metadata ) );
        }

        return list;
    }
}
