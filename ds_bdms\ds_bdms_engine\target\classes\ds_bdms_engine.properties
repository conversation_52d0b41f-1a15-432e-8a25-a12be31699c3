##################### MySQL proxy IP #######################
mysql.proxy.ip=127.0.0.1
##################### MySQL proxy IP  #######################

###########################敏感数据发现任务、文件脱敏任务本地缓存路径-Start###########################
#FTP文件下载本地保存路径
ftp.download.save.localpath=D:/CtyunWork/download/ftp
#HDFS文件下载本地保存路径
hdfs.download.save.localpath=D:/CtyunWork/download/hdfs
#linux文件下载本地保存路径
linux.download.save.localpath=D:/CtyunWork/download/linux
minio.download.save.localpath=D:/CtyunWork/download/minio
###########################敏感数据发现任务、文件脱敏任务本地缓存路径--End###########################


###########################端口扫描-Start###########################
#端口扫描结果本地保存路径
scanner.host.save.localpath=D:/CtyunWork/download/host
#端口扫描连接超时(毫秒)
scanner.host.connect.timeout=500
#端口扫描线程数
scanner.host.thread.num=100
###########################端口扫描-End###########################


###########################Hive-Start###########################
#连接超时
hiveConnectionTimeout=30000
#抽取数据临时表
hiveoutputtablenameprefix=sjfw_ubd_sec
#hiveUrlPrincipal=hive/<EMAIL>
#hiveUrlPrincipal=hive/<EMAIL>
hiveUrlPrincipal=
#hive Queuename
hiveQueuename=root.bigdata.motl.mt2
###########################Hive-End###########################

# ----- KERBEROS 生产环境配置 -----
hive.kerberos.conf.path=/data/software/ddms/system/krb5.conf
hive.kerberos.keytab=/data/software/ddms/system/hadoop.keytab
hive.kerberos.jdbc.user=<EMAIL>
hive.kerberos.jdbc.jaasFilePath=/jaas.conf
hive.kerberos.jdbc.url=********************************,hdp04.bonc.com:2181,hdp05.bonc.com:2181/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2;principal=hs2/<EMAIL>
# ----- KERBEROS 生产环境配置 -----

###########################HDFS&Hive的Kerberos认证-Start###########################
#认证方式1、2、3,4 配置0为不进行kerberos认证
kb.auth.type=0
#keberos用户名
kb.keytabUser=hdfs
#keytab文件存放路径
kb.keytabPath=/home/<USER>/hdfs.keytab
#kb.keytabPath=
#krb5 principal
kb.Principal=hive/<EMAIL>
#kb.Principal=
#krb5.conf文件存放路径
kb.krb5Path=/etc/krb5.conf
#kb.krb5Path=
# core-site.xml文件存放路径
kb.coreSite=/home/<USER>/FSDataAudit/auth/conf/core-site.xml
#kb.coreSite=
# hdfs-site.xml文件存放路径
kb.hdfsSite=/home/<USER>/FSDataAudit/auth/conf/hdfs-site.xml
#kb.hdfsSite=
# yarn-site.xml文件存放路径
kb.yarnSite=/home/<USER>/FSDataAudit/auth/conf/yarn-site.xml
#kb.yarnSite=
###########################HDFS&Hive的Kerberos认证-End###########################


###########################数据库脱敏-Start###########################
#批次读取数据条数
dbtask.readdatacount=100000
#批次写出数据条数
dbtask.writedatacount=1000
###########################数据库脱敏-End###########################

###########################水印注入-Start###########################
#水印行间距(每隔x行注入水印，不满x行则随机一行注入水印)
waterLineSpacing=500
###########################水印注入-End############################

###########################Kafka脱敏-Start###########################
kafka.consumer.bootstrap-servers=127.0.0.1:9092
kafka.consumer.group-id=bsm
kafka.consumer.enable.auto.commit=true
kafka.consumer.auto.commit.interval.ms=1000
kafka.consumer.session.timeout.ms=30000
kafka.consumer.key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
kafka.consumer.value.deserializer=org.apache.kafka.common.serialization.StringDeserializer
#kafka脱敏结果统计周期(秒)
kafka.mask.result.cycle=10
#kafka脱敏结果数据保留时长(分钟)
kafka.mask.data.timeout=10
###########################Kafka脱敏-End###########################


###########################脱敏审计-Start###########################
#脱敏日志审计
#偏移量，默认检测前一天
maskaudit.log.interval.day=1
#yyyMMdd格式
maskaudit.log.date=
maskaudit.log.clean.path=F:\\maskaudit\\afterpath

#脱敏Jar包审计
#偏移量，默认检测前一天
maskaudit.jarlog.interval.day=1
#yyyMMdd格式
maskaudit.jarlog.date=
###########################脱敏审计-End###########################


###########################hbase集群地址###########################
#hbase集群地址
hbase.zookeeper.quorum=10.34.1.87:2181,10.34.1.88:2181,10.34.1.89:2181,10.34.1.90:2181,10.34.1.91:2181
###########################hbase集群地址###########################


#######################################动态脱敏系统业务相关配置#######################################

############################################ Database proxy configuration start ############################################

##################### Oracle proxy start <已重构,页面配置>  #######################
#Oracle动态脱敏执行引擎开关
oracle.sdd.enable=false
#Oracle动态脱敏执行引擎所监听的主机端口号
oracle.sdd.listen.port=3044

# 被代理的实际Oracle数据库配置
oracle.db.driver=oracle.jdbc.OracleDriver
oracle.db.url=****************************************
oracle.db.username=test
oracle.db.password=123456
# 被代理的实际Oracle数据库配置
oracle.db.host=************
oracle.db.port=1521
##################### Oracle proxy end  #######################

##################### MySQL proxy start <已重构,页面配置>  #######################
#MySQL动态脱敏执行引擎开关
mysql.sdd.enable=true
#MySQL动态脱敏执行引擎所监听的主机端口号
mysql.sdd.listen.port=3046
# 被代理的实际MySQL数据库配置
mysql.db.driver=com.mysql.jdbc.Driver
mysql.db.url=******************************
mysql.db.username=demo
mysql.db.password=demo
# 被代理的实际MySQL数据库配置
mysql.db.host=************
mysql.db.port=3306
##################### MySQL proxy end  #######################

##################### MySQL and Oracle common start  #######################
#不支持语句类型拦截开关：1拦截，0放行
unsupportedTypeInterceptSwitch=0
#黑名单用户或IP拦截开关：1拦截，0放行
blacklistInterceptSwitch=1
#未配置脱敏策略拦截开关：1拦截，0放行
noConfigStrategyInterceptSwitch=0
#未配置用户拦截开关：1拦截，0放行
noConfigUserInterceptSwitch=1
#改写后SQL超长拦截开关：1拦截，0放行
sqlAfterRewritingIsTooLongInterceptSwitch=0
##################### MySQL and Oracle common end  #######################

############################################ Database proxy configuration end ############################################

############################################ API proxy configuration start ############################################
#API动态脱敏执行引擎开关
api.sdd.enable=false
#API动态脱敏执行引擎所监听的主机端口号
api.sdd.listen.port=3044
#API动态脱敏执行引擎SSL支持开关
api.sdd.ssl.enable=true
############################################ API proxy configuration end ############################################

pictureVideoMosaicImageName=mosaic_v1:latest

syslog.host=127.0.0.1
syslog.port=32376
