package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditResultV1;
import com.wzsec.modules.mask.service.dto.MaskAuditResultV1Dto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAuditResultV1MapperImpl implements MaskAuditResultV1Mapper {

    @Override
    public MaskAuditResultV1 toEntity(MaskAuditResultV1Dto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditResultV1 maskAuditResultV1 = new MaskAuditResultV1();

        maskAuditResultV1.setId( dto.getId() );
        maskAuditResultV1.setTaskname( dto.getTaskname() );
        maskAuditResultV1.setSystemid( dto.getSystemid() );
        maskAuditResultV1.setChecktype( dto.getChecktype() );
        maskAuditResultV1.setOutpath( dto.getOutpath() );
        maskAuditResultV1.setRisk( dto.getRisk() );
        maskAuditResultV1.setCount( dto.getCount() );
        maskAuditResultV1.setChecktime( dto.getChecktime() );
        maskAuditResultV1.setSubmitter( dto.getSubmitter() );
        maskAuditResultV1.setSparefield1( dto.getSparefield1() );
        maskAuditResultV1.setSparefield2( dto.getSparefield2() );
        maskAuditResultV1.setSparefield3( dto.getSparefield3() );
        maskAuditResultV1.setSparefield4( dto.getSparefield4() );

        return maskAuditResultV1;
    }

    @Override
    public MaskAuditResultV1Dto toDto(MaskAuditResultV1 entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditResultV1Dto maskAuditResultV1Dto = new MaskAuditResultV1Dto();

        maskAuditResultV1Dto.setId( entity.getId() );
        maskAuditResultV1Dto.setTaskname( entity.getTaskname() );
        maskAuditResultV1Dto.setSystemid( entity.getSystemid() );
        maskAuditResultV1Dto.setChecktype( entity.getChecktype() );
        maskAuditResultV1Dto.setOutpath( entity.getOutpath() );
        maskAuditResultV1Dto.setRisk( entity.getRisk() );
        maskAuditResultV1Dto.setCount( entity.getCount() );
        maskAuditResultV1Dto.setChecktime( entity.getChecktime() );
        maskAuditResultV1Dto.setSubmitter( entity.getSubmitter() );
        maskAuditResultV1Dto.setSparefield1( entity.getSparefield1() );
        maskAuditResultV1Dto.setSparefield2( entity.getSparefield2() );
        maskAuditResultV1Dto.setSparefield3( entity.getSparefield3() );
        maskAuditResultV1Dto.setSparefield4( entity.getSparefield4() );

        return maskAuditResultV1Dto;
    }

    @Override
    public List<MaskAuditResultV1> toEntity(List<MaskAuditResultV1Dto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditResultV1> list = new ArrayList<MaskAuditResultV1>( dtoList.size() );
        for ( MaskAuditResultV1Dto maskAuditResultV1Dto : dtoList ) {
            list.add( toEntity( maskAuditResultV1Dto ) );
        }

        return list;
    }

    @Override
    public List<MaskAuditResultV1Dto> toDto(List<MaskAuditResultV1> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditResultV1Dto> list = new ArrayList<MaskAuditResultV1Dto>( entityList.size() );
        for ( MaskAuditResultV1 maskAuditResultV1 : entityList ) {
            list.add( toDto( maskAuditResultV1 ) );
        }

        return list;
    }
}
