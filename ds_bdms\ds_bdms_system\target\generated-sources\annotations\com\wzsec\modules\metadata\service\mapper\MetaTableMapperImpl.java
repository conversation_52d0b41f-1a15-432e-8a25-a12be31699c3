package com.wzsec.modules.metadata.service.mapper;

import com.wzsec.modules.metadata.domain.MetaTable;
import com.wzsec.modules.metadata.service.dto.MetaTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MetaTableMapperImpl implements MetaTableMapper {

    @Override
    public MetaTable toEntity(MetaTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        MetaTable metaTable = new MetaTable();

        metaTable.setId( dto.getId() );
        metaTable.setTaskname( dto.getTaskname() );
        metaTable.setDbname( dto.getDbname() );
        metaTable.setDbstatus( dto.getDbstatus() );
        metaTable.setTablename( dto.getTablename() );
        metaTable.setTablecname( dto.getTablecname() );
        metaTable.setTablestatus( dto.getTablestatus() );
        metaTable.setSourcetype( dto.getSourcetype() );
        metaTable.setSourcename( dto.getSourcename() );
        metaTable.setSourceid( dto.getSourceid() );
        metaTable.setCategory( dto.getCategory() );
        metaTable.setSenlevel( dto.getSenlevel() );
        metaTable.setSubjectdomain( dto.getSubjectdomain() );
        metaTable.setStorepath( dto.getStorepath() );
        metaTable.setEvaluationmethod( dto.getEvaluationmethod() );
        metaTable.setNote( dto.getNote() );
        metaTable.setCreateuser( dto.getCreateuser() );
        metaTable.setCreatetime( dto.getCreatetime() );
        metaTable.setUpdateuser( dto.getUpdateuser() );
        metaTable.setUpdatetime( dto.getUpdatetime() );
        metaTable.setSparefield1( dto.getSparefield1() );
        metaTable.setSparefield2( dto.getSparefield2() );
        metaTable.setSparefield3( dto.getSparefield3() );
        metaTable.setSparefield4( dto.getSparefield4() );
        metaTable.setGetway( dto.getGetway() );
        metaTable.setEvaluationstatus( dto.getEvaluationstatus() );

        return metaTable;
    }

    @Override
    public MetaTableDto toDto(MetaTable entity) {
        if ( entity == null ) {
            return null;
        }

        MetaTableDto metaTableDto = new MetaTableDto();

        metaTableDto.setId( entity.getId() );
        metaTableDto.setTaskname( entity.getTaskname() );
        metaTableDto.setDbname( entity.getDbname() );
        metaTableDto.setDbstatus( entity.getDbstatus() );
        metaTableDto.setTablename( entity.getTablename() );
        metaTableDto.setTablecname( entity.getTablecname() );
        metaTableDto.setTablestatus( entity.getTablestatus() );
        metaTableDto.setSourcetype( entity.getSourcetype() );
        metaTableDto.setSourcename( entity.getSourcename() );
        metaTableDto.setSourceid( entity.getSourceid() );
        metaTableDto.setCategory( entity.getCategory() );
        metaTableDto.setSenlevel( entity.getSenlevel() );
        metaTableDto.setSubjectdomain( entity.getSubjectdomain() );
        metaTableDto.setStorepath( entity.getStorepath() );
        metaTableDto.setEvaluationmethod( entity.getEvaluationmethod() );
        metaTableDto.setNote( entity.getNote() );
        metaTableDto.setCreateuser( entity.getCreateuser() );
        metaTableDto.setCreatetime( entity.getCreatetime() );
        metaTableDto.setUpdateuser( entity.getUpdateuser() );
        metaTableDto.setUpdatetime( entity.getUpdatetime() );
        metaTableDto.setSparefield1( entity.getSparefield1() );
        metaTableDto.setSparefield2( entity.getSparefield2() );
        metaTableDto.setSparefield3( entity.getSparefield3() );
        metaTableDto.setSparefield4( entity.getSparefield4() );
        metaTableDto.setGetway( entity.getGetway() );
        metaTableDto.setEvaluationstatus( entity.getEvaluationstatus() );

        return metaTableDto;
    }

    @Override
    public List<MetaTable> toEntity(List<MetaTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MetaTable> list = new ArrayList<MetaTable>( dtoList.size() );
        for ( MetaTableDto metaTableDto : dtoList ) {
            list.add( toEntity( metaTableDto ) );
        }

        return list;
    }

    @Override
    public List<MetaTableDto> toDto(List<MetaTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetaTableDto> list = new ArrayList<MetaTableDto>( entityList.size() );
        for ( MetaTable metaTable : entityList ) {
            list.add( toDto( metaTable ) );
        }

        return list;
    }
}
