package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:23+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DbtaskconfigMapperImpl implements DbtaskconfigMapper {

    @Override
    public DBTaskConfig toEntity(DBTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        DBTaskConfig dBTaskConfig = new DBTaskConfig();

        dBTaskConfig.setId( dto.getId() );
        dBTaskConfig.setCreatetime( dto.getCreatetime() );
        dBTaskConfig.setCreateuser( dto.getCreateuser() );
        dBTaskConfig.setCron( dto.getCron() );
        dBTaskConfig.setErrordirectory( dto.getErrordirectory() );
        dBTaskConfig.setExecutionstate( dto.getExecutionstate() );
        dBTaskConfig.setInputdatasourceid( dto.getInputdatasourceid() );
        dBTaskConfig.setOutputdatasourceid( dto.getOutputdatasourceid() );
        dBTaskConfig.setOutputdirectory( dto.getOutputdirectory() );
        dBTaskConfig.setOutputtype( dto.getOutputtype() );
        dBTaskConfig.setIswatermark( dto.getIswatermark() );
        dBTaskConfig.setDataprovider( dto.getDataprovider() );
        dBTaskConfig.setDatause( dto.getDatause() );
        dBTaskConfig.setWatermarkcol( dto.getWatermarkcol() );
        dBTaskConfig.setRemark( dto.getRemark() );
        dBTaskConfig.setSparefield1( dto.getSparefield1() );
        dBTaskConfig.setSparefield2( dto.getSparefield2() );
        dBTaskConfig.setSparefield3( dto.getSparefield3() );
        dBTaskConfig.setSparefield4( dto.getSparefield4() );
        dBTaskConfig.setSparefield5( dto.getSparefield5() );
        dBTaskConfig.setState( dto.getState() );
        dBTaskConfig.setStrategyid( dto.getStrategyid() );
        dBTaskConfig.setSubmittype( dto.getSubmittype() );
        dBTaskConfig.setTablename( dto.getTablename() );
        dBTaskConfig.setTaskname( dto.getTaskname() );
        dBTaskConfig.setUpdatetime( dto.getUpdatetime() );
        dBTaskConfig.setUpdateuser( dto.getUpdateuser() );

        return dBTaskConfig;
    }

    @Override
    public DBTaskConfigDto toDto(DBTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        DBTaskConfigDto dBTaskConfigDto = new DBTaskConfigDto();

        dBTaskConfigDto.setId( entity.getId() );
        dBTaskConfigDto.setCreatetime( entity.getCreatetime() );
        dBTaskConfigDto.setCreateuser( entity.getCreateuser() );
        dBTaskConfigDto.setCron( entity.getCron() );
        dBTaskConfigDto.setErrordirectory( entity.getErrordirectory() );
        dBTaskConfigDto.setExecutionstate( entity.getExecutionstate() );
        dBTaskConfigDto.setInputdatasourceid( entity.getInputdatasourceid() );
        dBTaskConfigDto.setOutputdatasourceid( entity.getOutputdatasourceid() );
        dBTaskConfigDto.setOutputdirectory( entity.getOutputdirectory() );
        dBTaskConfigDto.setOutputtype( entity.getOutputtype() );
        dBTaskConfigDto.setIswatermark( entity.getIswatermark() );
        dBTaskConfigDto.setDataprovider( entity.getDataprovider() );
        dBTaskConfigDto.setDatause( entity.getDatause() );
        dBTaskConfigDto.setWatermarkcol( entity.getWatermarkcol() );
        dBTaskConfigDto.setRemark( entity.getRemark() );
        dBTaskConfigDto.setSparefield1( entity.getSparefield1() );
        dBTaskConfigDto.setSparefield2( entity.getSparefield2() );
        dBTaskConfigDto.setSparefield3( entity.getSparefield3() );
        dBTaskConfigDto.setSparefield4( entity.getSparefield4() );
        dBTaskConfigDto.setSparefield5( entity.getSparefield5() );
        dBTaskConfigDto.setState( entity.getState() );
        dBTaskConfigDto.setStrategyid( entity.getStrategyid() );
        dBTaskConfigDto.setSubmittype( entity.getSubmittype() );
        dBTaskConfigDto.setTablename( entity.getTablename() );
        dBTaskConfigDto.setTaskname( entity.getTaskname() );
        dBTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        dBTaskConfigDto.setUpdateuser( entity.getUpdateuser() );

        return dBTaskConfigDto;
    }

    @Override
    public List<DBTaskConfig> toEntity(List<DBTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DBTaskConfig> list = new ArrayList<DBTaskConfig>( dtoList.size() );
        for ( DBTaskConfigDto dBTaskConfigDto : dtoList ) {
            list.add( toEntity( dBTaskConfigDto ) );
        }

        return list;
    }

    @Override
    public List<DBTaskConfigDto> toDto(List<DBTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DBTaskConfigDto> list = new ArrayList<DBTaskConfigDto>( entityList.size() );
        for ( DBTaskConfig dBTaskConfig : entityList ) {
            list.add( toDto( dBTaskConfig ) );
        }

        return list;
    }
}
