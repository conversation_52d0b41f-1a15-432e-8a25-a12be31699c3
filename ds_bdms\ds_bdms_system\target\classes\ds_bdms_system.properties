#######################################敏感数据发现业务相关配置#######################################

###########################敏感数据发现任务-Start###########################
#FTP文件下载本地保存路径
ftp.download.save.localpath=D:/CtyunWork//download/ftp
#HDFS文件下载本地保存路径
hdfs.download.save.localpath=D:/CtyunWork/download/hdfs
#linux文件下载本地保存路径
linux.download.save.localpath=D:/CtyunWork/download/linux
###########################敏感数据发现任务-End###########################

###########################端口扫描-Start###########################
#端口扫描结果本地保存路径
scanner.host.save.localpath=D:/CtyunWork/scanner/host
#端口扫描连接超时(毫秒)
scanner.host.connect.timeout=500
#端口扫描线程数
scanner.host.thread.num=100
###########################端口扫描-End###########################

###########################引擎地址-Start###########################
#数据源测试连接
engine.source.testconn=/engine/datasource/test/
#数据库如果不存在新增数据库
engine.source.createconn=/engine/datasource/createDataBaseIfNoExist/


#执行检测动脱代理服务器状态
engine.proxy.serverstatus=/engine/proxyConfig/test/

#执行服务器重启指令
engine.proxy.executeRestart=/engine/proxyConfig/executeRestart/

#执行敏感数据发现任务
engine.dotask=/engine/task/exec/
#定时执行敏感数据发现任务
engine.dotimingtask=/engine/task/timingexec/

#执行数据库脱敏任务
engine.db.dotask=/engine/db/task/exec/
#定时执行数据库脱敏任务
engine.db.dotimingtask=/engine/db/task/timingexec/
# 在引擎执行指令
engine.db.commandtask=/engine/db/task/command/

#执行数据库批量脱敏任务
engine.dbbatch.dotask=/engine/dbbatch/task/exec/
#定时执行数据库批量脱敏任务
engine.dbbatch.dotimingtask=/engine/dbbatch/task/timingexec/
#获取数据库脱敏结果预览
engine.dbbatch.maskresultrreview=/engine/dbbatch/task/maskresultrreview/

#执行文件脱敏任务
engine.file.dotask=/engine/file/task/exec/
#定时执行文件脱敏任务
engine.file.dotimingtask=/engine/file/task/timingexec/
#获取文件脱敏结果预览
engine.file.maskresultrreview=/engine/file/task/maskresultrreview/

#执行hdfs脱敏任务
engine.hdfs.dotask=/engine/hdfs/task/exec/

#执行hive脱敏任务
engine.hive.dotask=/engine/hive/task/exec/

#执行kafka脱敏任务
engine.kafka.dotask=/engine/kafka/task/exec/
#停止kafka脱敏任务
engine.kafka.stoptask=/engine/kafka/task/stop/

#执行图片脱敏任务
engine.picture.dotask=/engine/picture/task/exec/
engine.picture.dotimingtask=/engine/picture/task/timingexec/

#执行视频脱敏任务
engine.video.dotask=/engine/video/task/exec/
engine.video.dotimingtask=/engine/video/task/timingexec/

#执行匿名化脱敏任务
engine.anonymization.dotask=/engine/anonymization/task/exec/

#执行HBase脱敏任务
engine.hbase.dotask=/engine/hbase/task/exec/


#执行脱敏审计任务
engine.maskaudit.dotask=/engine/maskaudit/task/exec/
#定时执行脱敏审计任务
engine.maskaudit.dotimingtask=/engine/maskaudit/task/timingexec/
###########################引擎地址-End###########################

###########################溯源文件上路径-Start###########################
traceFileUploadingPath=C:/
###########################溯源文件上传路径-End############################

###########################hbase集群地址###########################
#hbase集群地址
hbase.zookeeper.quorum=**********:2181,**********:2181,**********:2181,**********:2181,**********:2181
###########################hbase集群地址###########################

#################新增用户默认配置#################
user.default.password=Ct$Ad@20
user.default.roles=2,4,6
user.default.job=11
user.default.dept=2

syslog.host=127.0.0.1
syslog.port=32376


######################################## 配置策略时从分类分级系统获取参考等级 ########################################
#要参考的分类分级系统数据库信息
bdcl.Driver=com.mysql.jdbc.Driver
bdcl.Url=*************************************************************************************************************************
bdcl.UserName=root
bdcl.Password=<EMAIL>


######################################## 水印信息配置-Start ########################################
#水印数据读取数量
watermark.data.read.count=1000
######################################## 水印信息配置-End ########################################


####################################### 管网SSO单点登录 #######################################
gw.sso.client_id=DSBDMS
gw.sso.client_secret=5a3eed9bef2a8ebfc21dbc0ab7a45539
gw.sso.getTokenUrl=http://***********:80/portal/oauth2/getToken
gw.sso.checkTokenUrl=http://***********:80/portal/oauth2/checkTokenValid
gw.sso.getUserInfoUrl=http://***********:80/portal/oauth2/getUserInfo

#设置的默认部门、岗位、角色id
gw.sso.defaultDeptId=2
gw.sso.defaultJobId=11
gw.sso.defaultRuleId=2,4
gw.qianxin.sso.publicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkS+/dEbEHJYTyPrSY+xC2MyJVUYSIEutmMGYSd2MVjy/HJgH8T+VO5it9hWII+164/jiHjMX2kPdH4uH+N1zcbIdxSVb9t0wl2T3NKVPpq/3IRP3J+QGKeoABV41gBCGhtsPAKbQY2ra3cFFTd1fYA8hVc+I3AlWUbIat1zzVMp4t31W//VM+gNBXSycryuExRodhnJkLkuoH5qlhbvKXj0PRSGRNH0dRiyT5KveGTlMA65UaCFv6HIja38HHuTlMgErUIIw/PatHc594O3YVNN5O3CPkeOm39k326PzdL7orfIcLzP4AiD/qu6cr8FgtG0GD6YOLKxq//Q24BUsRQIDAQAB
#脱敏文件下载临时存储ftp、sftp配置 分别为：ip,端口，用户名，密码，本地文件存储路径
maskfilestore.ftp.ip=*************
maskfilestore.ftp.port=22
maskfilestore.ftp.username=root
maskfilestore.ftp.password=<EMAIL>.!@0501
maskfilestore.localDir=D:\\cs3\\

#SDK插件下载路径地址
sdk.download.dir=D:/GitEE/ds_bdms/ds_bdms/ds_bdms_engine/target/ds_bdms_engine-3.2.6.jar

#文件上传地址
file.upload.dir=D:\\bjwz\\mask\\ds_bdms\\ds_bdms\\file\\upload


