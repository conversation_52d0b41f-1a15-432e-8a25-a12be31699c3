package com.wzsec.modules.sdd.metadata.service.mapper;

import com.wzsec.modules.sdd.metadata.domain.MetaField;
import com.wzsec.modules.sdd.metadata.service.dto.MetaFieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:23+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MetaFieldMapperImpl implements MetaFieldMapper {

    @Override
    public MetaField toEntity(MetaFieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        MetaField metaField = new MetaField();

        metaField.setId( dto.getId() );
        metaField.setMtid( dto.getMtid() );
        metaField.setFieldname( dto.getFieldname() );
        metaField.setFieldcname( dto.getFieldcname() );
        metaField.setFieldsname( dto.getFieldsname() );
        metaField.setFieldscname( dto.getFieldscname() );
        metaField.setExample( dto.getExample() );
        metaField.setFieldstatus( dto.getFieldstatus() );
        metaField.setCategory( dto.getCategory() );
        metaField.setSenlevel( dto.getSenlevel() );
        metaField.setAlgorithmid( dto.getAlgorithmid() );
        metaField.setNote( dto.getNote() );
        metaField.setCreateuser( dto.getCreateuser() );
        metaField.setCreatetime( dto.getCreatetime() );
        metaField.setUpdateuser( dto.getUpdateuser() );
        metaField.setUpdatetime( dto.getUpdatetime() );
        metaField.setSparefield1( dto.getSparefield1() );
        metaField.setSparefield2( dto.getSparefield2() );
        metaField.setSparefield3( dto.getSparefield3() );
        metaField.setSparefield4( dto.getSparefield4() );

        return metaField;
    }

    @Override
    public MetaFieldDto toDto(MetaField entity) {
        if ( entity == null ) {
            return null;
        }

        MetaFieldDto metaFieldDto = new MetaFieldDto();

        metaFieldDto.setId( entity.getId() );
        metaFieldDto.setMtid( entity.getMtid() );
        metaFieldDto.setFieldname( entity.getFieldname() );
        metaFieldDto.setFieldcname( entity.getFieldcname() );
        metaFieldDto.setFieldsname( entity.getFieldsname() );
        metaFieldDto.setFieldscname( entity.getFieldscname() );
        metaFieldDto.setExample( entity.getExample() );
        metaFieldDto.setFieldstatus( entity.getFieldstatus() );
        metaFieldDto.setCategory( entity.getCategory() );
        metaFieldDto.setSenlevel( entity.getSenlevel() );
        metaFieldDto.setAlgorithmid( entity.getAlgorithmid() );
        metaFieldDto.setNote( entity.getNote() );
        metaFieldDto.setCreateuser( entity.getCreateuser() );
        metaFieldDto.setCreatetime( entity.getCreatetime() );
        metaFieldDto.setUpdateuser( entity.getUpdateuser() );
        metaFieldDto.setUpdatetime( entity.getUpdatetime() );
        metaFieldDto.setSparefield1( entity.getSparefield1() );
        metaFieldDto.setSparefield2( entity.getSparefield2() );
        metaFieldDto.setSparefield3( entity.getSparefield3() );
        metaFieldDto.setSparefield4( entity.getSparefield4() );

        return metaFieldDto;
    }

    @Override
    public List<MetaField> toEntity(List<MetaFieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MetaField> list = new ArrayList<MetaField>( dtoList.size() );
        for ( MetaFieldDto metaFieldDto : dtoList ) {
            list.add( toEntity( metaFieldDto ) );
        }

        return list;
    }

    @Override
    public List<MetaFieldDto> toDto(List<MetaField> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetaFieldDto> list = new ArrayList<MetaFieldDto>( entityList.size() );
        for ( MetaField metaField : entityList ) {
            list.add( toDto( metaField ) );
        }

        return list;
    }
}
