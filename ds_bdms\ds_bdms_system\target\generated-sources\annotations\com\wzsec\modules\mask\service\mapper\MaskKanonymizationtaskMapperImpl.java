package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskKanonymizationtask;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationtaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskKanonymizationtaskMapperImpl implements MaskKanonymizationtaskMapper {

    @Override
    public MaskKanonymizationtask toEntity(MaskKanonymizationtaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskKanonymizationtask maskKanonymizationtask = new MaskKanonymizationtask();

        maskKanonymizationtask.setId( dto.getId() );
        maskKanonymizationtask.setTaskname( dto.getTaskname() );
        maskKanonymizationtask.setState( dto.getState() );
        maskKanonymizationtask.setDatabasename( dto.getDatabasename() );
        maskKanonymizationtask.setDbname( dto.getDbname() );
        maskKanonymizationtask.setTabname( dto.getTabname() );
        maskKanonymizationtask.setUrl( dto.getUrl() );
        maskKanonymizationtask.setUsername( dto.getUsername() );
        maskKanonymizationtask.setPass( dto.getPass() );
        maskKanonymizationtask.setStrategy( dto.getStrategy() );
        maskKanonymizationtask.setOutputdirectory( dto.getOutputdirectory() );
        maskKanonymizationtask.setErrordirectory( dto.getErrordirectory() );
        maskKanonymizationtask.setCreateuserid( dto.getCreateuserid() );
        maskKanonymizationtask.setCreatetime( dto.getCreatetime() );
        maskKanonymizationtask.setUpdateuserid( dto.getUpdateuserid() );
        maskKanonymizationtask.setUpdatetime( dto.getUpdatetime() );
        maskKanonymizationtask.setRemark( dto.getRemark() );
        maskKanonymizationtask.setSparefield1( dto.getSparefield1() );
        maskKanonymizationtask.setSparefield2( dto.getSparefield2() );
        maskKanonymizationtask.setSparefield3( dto.getSparefield3() );
        maskKanonymizationtask.setSparefield4( dto.getSparefield4() );
        maskKanonymizationtask.setSparefield5( dto.getSparefield5() );

        return maskKanonymizationtask;
    }

    @Override
    public MaskKanonymizationtaskDto toDto(MaskKanonymizationtask entity) {
        if ( entity == null ) {
            return null;
        }

        MaskKanonymizationtaskDto maskKanonymizationtaskDto = new MaskKanonymizationtaskDto();

        maskKanonymizationtaskDto.setId( entity.getId() );
        maskKanonymizationtaskDto.setTaskname( entity.getTaskname() );
        maskKanonymizationtaskDto.setState( entity.getState() );
        maskKanonymizationtaskDto.setDatabasename( entity.getDatabasename() );
        maskKanonymizationtaskDto.setDbname( entity.getDbname() );
        maskKanonymizationtaskDto.setTabname( entity.getTabname() );
        maskKanonymizationtaskDto.setUrl( entity.getUrl() );
        maskKanonymizationtaskDto.setUsername( entity.getUsername() );
        maskKanonymizationtaskDto.setPass( entity.getPass() );
        maskKanonymizationtaskDto.setStrategy( entity.getStrategy() );
        maskKanonymizationtaskDto.setOutputdirectory( entity.getOutputdirectory() );
        maskKanonymizationtaskDto.setErrordirectory( entity.getErrordirectory() );
        maskKanonymizationtaskDto.setCreateuserid( entity.getCreateuserid() );
        maskKanonymizationtaskDto.setCreatetime( entity.getCreatetime() );
        maskKanonymizationtaskDto.setUpdateuserid( entity.getUpdateuserid() );
        maskKanonymizationtaskDto.setUpdatetime( entity.getUpdatetime() );
        maskKanonymizationtaskDto.setRemark( entity.getRemark() );
        maskKanonymizationtaskDto.setSparefield1( entity.getSparefield1() );
        maskKanonymizationtaskDto.setSparefield2( entity.getSparefield2() );
        maskKanonymizationtaskDto.setSparefield3( entity.getSparefield3() );
        maskKanonymizationtaskDto.setSparefield4( entity.getSparefield4() );
        maskKanonymizationtaskDto.setSparefield5( entity.getSparefield5() );

        return maskKanonymizationtaskDto;
    }

    @Override
    public List<MaskKanonymizationtask> toEntity(List<MaskKanonymizationtaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskKanonymizationtask> list = new ArrayList<MaskKanonymizationtask>( dtoList.size() );
        for ( MaskKanonymizationtaskDto maskKanonymizationtaskDto : dtoList ) {
            list.add( toEntity( maskKanonymizationtaskDto ) );
        }

        return list;
    }

    @Override
    public List<MaskKanonymizationtaskDto> toDto(List<MaskKanonymizationtask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskKanonymizationtaskDto> list = new ArrayList<MaskKanonymizationtaskDto>( entityList.size() );
        for ( MaskKanonymizationtask maskKanonymizationtask : entityList ) {
            list.add( toDto( maskKanonymizationtask ) );
        }

        return list;
    }
}
