package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.DBTaskConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2020-11-11
 */
@Transactional
public interface DBTaskConfigRepository extends JpaRepository<DBTaskConfig, Integer>, JpaSpecificationExecutor<DBTaskConfig> {

    @Query(value = "select MAX(`taskname`) from sdd_mask_dbtaskconfig where taskname like concat(?1,'%')", nativeQuery = true)
    String findMAXTaskNameByPrefix(String prefix);


    /**
     * 根据ID修改状态
     *
     * @param id
     */
    @Modifying
    @Query(value = "UPDATE sdd_mask_dbtaskconfig SET executionstate =?2 WHERE id=?1 ", nativeQuery = true)
    void updateTaskStatus(Integer id, String status);

    /**
     * 通过任务号查询
     * @param taskName
     * @return
     */
    @Query(value = "select * from sdd_mask_dbtaskconfig where taskname = ?1", nativeQuery = true)
    DBTaskConfig findByTaskName(String taskName);
}
