package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcRule;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class JdbcRuleMapperImpl implements JdbcRuleMapper {

    @Override
    public JdbcRule toEntity(JdbcRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcRule jdbcRule = new JdbcRule();

        jdbcRule.setId( dto.getId() );
        jdbcRule.setSname( dto.getSname() );
        jdbcRule.setAlgorithmid( dto.getAlgorithmid() );
        jdbcRule.setParam( dto.getParam() );
        jdbcRule.setRole( dto.getRole() );
        jdbcRule.setState( dto.getState() );
        jdbcRule.setCreateuser( dto.getCreateuser() );
        jdbcRule.setCreatetime( dto.getCreatetime() );
        jdbcRule.setUpdateuser( dto.getUpdateuser() );
        jdbcRule.setUpdatetime( dto.getUpdatetime() );
        jdbcRule.setNote( dto.getNote() );
        jdbcRule.setSparefield1( dto.getSparefield1() );
        jdbcRule.setSparefield2( dto.getSparefield2() );
        jdbcRule.setSparefield3( dto.getSparefield3() );
        jdbcRule.setSparefield4( dto.getSparefield4() );
        jdbcRule.setSparefield5( dto.getSparefield5() );

        return jdbcRule;
    }

    @Override
    public JdbcRuleDto toDto(JdbcRule entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcRuleDto jdbcRuleDto = new JdbcRuleDto();

        jdbcRuleDto.setId( entity.getId() );
        jdbcRuleDto.setSname( entity.getSname() );
        jdbcRuleDto.setAlgorithmid( entity.getAlgorithmid() );
        jdbcRuleDto.setParam( entity.getParam() );
        jdbcRuleDto.setRole( entity.getRole() );
        jdbcRuleDto.setState( entity.getState() );
        jdbcRuleDto.setCreateuser( entity.getCreateuser() );
        jdbcRuleDto.setCreatetime( entity.getCreatetime() );
        jdbcRuleDto.setUpdateuser( entity.getUpdateuser() );
        jdbcRuleDto.setUpdatetime( entity.getUpdatetime() );
        jdbcRuleDto.setNote( entity.getNote() );
        jdbcRuleDto.setSparefield1( entity.getSparefield1() );
        jdbcRuleDto.setSparefield2( entity.getSparefield2() );
        jdbcRuleDto.setSparefield3( entity.getSparefield3() );
        jdbcRuleDto.setSparefield4( entity.getSparefield4() );
        jdbcRuleDto.setSparefield5( entity.getSparefield5() );

        return jdbcRuleDto;
    }

    @Override
    public List<JdbcRule> toEntity(List<JdbcRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcRule> list = new ArrayList<JdbcRule>( dtoList.size() );
        for ( JdbcRuleDto jdbcRuleDto : dtoList ) {
            list.add( toEntity( jdbcRuleDto ) );
        }

        return list;
    }

    @Override
    public List<JdbcRuleDto> toDto(List<JdbcRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcRuleDto> list = new ArrayList<JdbcRuleDto>( entityList.size() );
        for ( JdbcRule jdbcRule : entityList ) {
            list.add( toDto( jdbcRule ) );
        }

        return list;
    }
}
