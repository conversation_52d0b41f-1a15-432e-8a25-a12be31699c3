package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.TasksynrecordDetail;
import com.wzsec.modules.statistics.service.dto.TasksynrecordDetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TasksynrecordDetailMapperImpl implements TasksynrecordDetailMapper {

    @Override
    public TasksynrecordDetail toEntity(TasksynrecordDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        TasksynrecordDetail tasksynrecordDetail = new TasksynrecordDetail();

        tasksynrecordDetail.setId( dto.getId() );
        tasksynrecordDetail.setTaskname( dto.getTaskname() );
        tasksynrecordDetail.setTasknumber( dto.getTasknumber() );
        tasksynrecordDetail.setFieldname( dto.getFieldname() );
        tasksynrecordDetail.setFieldcname( dto.getFieldcname() );
        tasksynrecordDetail.setDbname( dto.getDbname() );
        tasksynrecordDetail.setTablename( dto.getTablename() );
        tasksynrecordDetail.setTablecname( dto.getTablecname() );
        tasksynrecordDetail.setSyndetail( dto.getSyndetail() );
        tasksynrecordDetail.setCreateuser( dto.getCreateuser() );
        tasksynrecordDetail.setCreatetime( dto.getCreatetime() );
        tasksynrecordDetail.setSparefield1( dto.getSparefield1() );
        tasksynrecordDetail.setSparefield2( dto.getSparefield2() );
        tasksynrecordDetail.setSparefield3( dto.getSparefield3() );

        return tasksynrecordDetail;
    }

    @Override
    public TasksynrecordDetailDto toDto(TasksynrecordDetail entity) {
        if ( entity == null ) {
            return null;
        }

        TasksynrecordDetailDto tasksynrecordDetailDto = new TasksynrecordDetailDto();

        tasksynrecordDetailDto.setId( entity.getId() );
        tasksynrecordDetailDto.setTaskname( entity.getTaskname() );
        tasksynrecordDetailDto.setTasknumber( entity.getTasknumber() );
        tasksynrecordDetailDto.setFieldname( entity.getFieldname() );
        tasksynrecordDetailDto.setFieldcname( entity.getFieldcname() );
        tasksynrecordDetailDto.setDbname( entity.getDbname() );
        tasksynrecordDetailDto.setTablename( entity.getTablename() );
        tasksynrecordDetailDto.setTablecname( entity.getTablecname() );
        tasksynrecordDetailDto.setSyndetail( entity.getSyndetail() );
        tasksynrecordDetailDto.setCreateuser( entity.getCreateuser() );
        tasksynrecordDetailDto.setCreatetime( entity.getCreatetime() );
        tasksynrecordDetailDto.setSparefield1( entity.getSparefield1() );
        tasksynrecordDetailDto.setSparefield2( entity.getSparefield2() );
        tasksynrecordDetailDto.setSparefield3( entity.getSparefield3() );

        return tasksynrecordDetailDto;
    }

    @Override
    public List<TasksynrecordDetail> toEntity(List<TasksynrecordDetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TasksynrecordDetail> list = new ArrayList<TasksynrecordDetail>( dtoList.size() );
        for ( TasksynrecordDetailDto tasksynrecordDetailDto : dtoList ) {
            list.add( toEntity( tasksynrecordDetailDto ) );
        }

        return list;
    }

    @Override
    public List<TasksynrecordDetailDto> toDto(List<TasksynrecordDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TasksynrecordDetailDto> list = new ArrayList<TasksynrecordDetailDto>( entityList.size() );
        for ( TasksynrecordDetail tasksynrecordDetail : entityList ) {
            list.add( toDto( tasksynrecordDetail ) );
        }

        return list;
    }
}
