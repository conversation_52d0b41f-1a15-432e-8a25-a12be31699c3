package com.wzsec.modules.mask.service.impl;

import com.wzsec.IntegerUtils;
import com.wzsec.modules.mask.domain.DBTaskConfig;
import com.wzsec.modules.mask.domain.FileTaskConfig;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import com.wzsec.modules.mask.service.dto.DBTaskConfigQueryCriteria;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.utils.*;
import com.wzsec.modules.mask.repository.DBTaskConfigRepository;
import com.wzsec.modules.mask.service.DBTaskConfigService;
import com.wzsec.modules.mask.service.mapper.DbtaskconfigMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
// 默认不使用缓存
//import org.springframework.cache.annotation.CacheConfig;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.*;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020-11-11
 */
@Service
//@CacheConfig(cacheNames = "dbtaskconfig")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DBTaskConfigServiceImpl implements DBTaskConfigService {

    private final DBTaskConfigRepository dbtaskconfigRepository;

    private final DbtaskconfigMapper dbtaskconfigMapper;

    private final UserService userService;

    private final DatasourceService datasourceService;

    public DBTaskConfigServiceImpl(DBTaskConfigRepository dbtaskconfigRepository, DbtaskconfigMapper dbtaskconfigMapper, UserService userService, DatasourceService datasourceService) {
        this.dbtaskconfigRepository = dbtaskconfigRepository;
        this.dbtaskconfigMapper = dbtaskconfigMapper;
        this.userService = userService;
        this.datasourceService = datasourceService;
    }

    @Override
    //@Cacheable(key = "#p0")
    public DBTaskConfigDto findById(Integer id) {
        DBTaskConfig dbtaskconfig = dbtaskconfigRepository.findById(id).orElseGet(DBTaskConfig::new);
        ValidationUtil.isNull(dbtaskconfig.getId(), "DBTaskConfig", "id", id);
        return dbtaskconfigMapper.toDto(dbtaskconfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public DBTaskConfigDto create(DBTaskConfig resources) {
        return dbtaskconfigMapper.toDto(dbtaskconfigRepository.save(resources));
    }

    @Override
    public String getMAXTaskName() {
        String nowDate = DateUtil.formatDate("yyyyMMdd", new Date());
        String taskno = dbtaskconfigRepository.findMAXTaskNameByPrefix(nowDate);
        int code = 0;
        if (StringUtils.isNotEmpty(taskno))
            code = Integer.parseInt(taskno.substring(taskno.length() - 3));
        return nowDate + IntegerUtils.autoGenericCode(code + 1, 3);
    }


    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(DBTaskConfig resources) {
        DBTaskConfig dbtaskconfig = dbtaskconfigRepository.findById(resources.getId()).orElseGet(DBTaskConfig::new);
        ValidationUtil.isNull(dbtaskconfig.getId(), "DBTaskConfig", "id", resources.getId());
        dbtaskconfig.copy(resources);
        dbtaskconfigRepository.save(dbtaskconfig);
    }


    @Override
//    @Cacheable(key = "'tree'")
    public Object getTabnameBySource(String sourceid) {
        List<Map<String, Object>> list = new LinkedList<>();
        if (null != sourceid && !"null".equals(sourceid)) {
            DatasourceDto byId = datasourceService.findById(Long.valueOf(sourceid));
            String srcurl = byId.getSrcurl();
            String type = byId.getType();
            String driverprogram = byId.getDriverprogram();
            String dbname = byId.getDbname();
            String username = byId.getUsername();
            //AES解密
            if (StringUtils.isNotEmpty(byId.getPassword())) {
                try {
                    String decrypt = AES.decrypt(byId.getPassword(), Const.AES_SECRET_KEY);
                    byId.setPassword(decrypt);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            String password = byId.getPassword();
            List<String> tablelist = getTableList(srcurl, type, driverprogram, dbname, username, password);
            if (tablelist != null) {
                for (String tabname : tablelist) {
                    Map<String, Object> map = new HashMap<>();
                    //map.put("id", level.getId());
                    map.put("value", tabname);
                    map.put("label", tabname);
                    list.add(map);
                }
            }
        }
        return list;
    }

    /**
     * @throws IOException
     * @Description:获取数据库表名
     * <AUTHOR> by dongs
     * @date 2020-11-12
     */
    private List<String> getTableList(String srcurl, String type, String driver, String dbname, String username, String password) {
        ArrayList<String> tablelist = null;
        if (Const.DB_MYSQL.equalsIgnoreCase(type) || Const.DB_UNIDB.equalsIgnoreCase(type) || Const.DB_RDS_MYSQL.equalsIgnoreCase(type) || Const.DB_DB2.equalsIgnoreCase(type)
                || Const.DB_SQLSERVER.equalsIgnoreCase(type) || Const.DB_INFORMIX.equalsIgnoreCase(type) || Const.DB_GBASE.equalsIgnoreCase(type) || Const.DB_GBASE8A.equalsIgnoreCase(type)
                || Const.DB_GBASE8S.equalsIgnoreCase(type)) {
            try {
                Class.forName(driver);
                // 连接数据库
                Connection con = DriverManager.getConnection(srcurl, username, password);
                ResultSet rs = null;
                // 根据数据库名查询相对应的表名
                DatabaseMetaData dbmd = (DatabaseMetaData) con.getMetaData();
                String[] types = {"TABLE"};
                rs = dbmd.getTables(null, null, "%", types);
                tablelist = new ArrayList<String>();
                while (rs.next()) {
                    tablelist.add(rs.getString("TABLE_NAME")); // 获取到表名
                }
            } catch (Exception e) {
                e.printStackTrace();
                tablelist = null;
            }
        } else if (Const.DB_ORACLE.equalsIgnoreCase(type)) {
            Connection con = null;
            ResultSet rs = null;
            try {
                // 连接数据库
                Class.forName(driver);
                con = DriverManager.getConnection(srcurl, username, password);
                DatabaseMetaData metaData = con.getMetaData();
                rs = metaData.getTables(null, username.toUpperCase(), null, new String[]{"TABLE"});
                tablelist = new ArrayList<String>();
                while (rs.next()) {
                    tablelist.add(rs.getString("TABLE_NAME")); // 获取到表名
                }
            } catch (Exception e) {
                tablelist = null;
            }
        } else if (Const.DB_PSOTGRESQL.equalsIgnoreCase(type) || Const.DB_RDS_PSOTGRESQL.equalsIgnoreCase(type)) {
            Connection con = null;
            ResultSet rs = null;
            try {
                // 连接数据库
                Class.forName(driver);
                con = DriverManager.getConnection(srcurl, username, password);
                DatabaseMetaData metaData = con.getMetaData();
                rs = metaData.getTables(null, username.toUpperCase(), null, new String[]{"TABLE"});
                tablelist = new ArrayList<String>();
                while (rs.next()) {
                    tablelist.add(rs.getString("TABLE_NAME")); // 获取到表名
                }
            } catch (Exception e) {
                tablelist = null;
            }
        } else if (Const.DB_DM.equalsIgnoreCase(type)) {
            Connection con = null;
            ResultSet rs = null;
            try {
                // 连接数据库
                Class.forName(driver);
                con = DriverManager.getConnection(srcurl, username, password);
                DatabaseMetaData metaData = con.getMetaData();
                rs = metaData.getTables(null, dbname.toUpperCase(), null, new String[]{"TABLE"});
                tablelist = new ArrayList<String>();
                while (rs.next()) {
                    tablelist.add(rs.getString("TABLE_NAME")); // 获取到表名
                }
            } catch (Exception e) {
                tablelist = null;
            }
        }

        return tablelist;
    }


    @Override
    public List<DBTaskConfigDto> getAllJob() {
        DBTaskConfigQueryCriteria criteria = new DBTaskConfigQueryCriteria();
        criteria.setSubmittype(Const.TASK_SUBMITTYPE_AUTO);
        criteria.setState(Const.TASK_STATE_USE);
        return dbtaskconfigMapper.toDto(dbtaskconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(Integer id, String status) {
        dbtaskconfigRepository.updateTaskStatus(id, status);
    }

    /**
     * 通过任务号查询
     * @param taskName
     * @return
     */
    @Override
    public DBTaskConfig findByTaskName(String taskName) {
        return dbtaskconfigRepository.findByTaskName(taskName);
    }


}
