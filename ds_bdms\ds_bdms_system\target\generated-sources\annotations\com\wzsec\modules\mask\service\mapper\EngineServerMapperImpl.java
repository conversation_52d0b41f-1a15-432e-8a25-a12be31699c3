package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.service.dto.EngineServerDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class EngineServerMapperImpl implements EngineServerMapper {

    @Override
    public EngineServer toEntity(EngineServerDto dto) {
        if ( dto == null ) {
            return null;
        }

        EngineServer engineServer = new EngineServer();

        engineServer.setId( dto.getId() );
        engineServer.setName( dto.getName() );
        engineServer.setIp( dto.getIp() );
        engineServer.setPort( dto.getPort() );
        engineServer.setApplication( dto.getApplication() );
        engineServer.setState( dto.getState() );
        engineServer.setIsvalid( dto.getIsvalid() );
        engineServer.setNote( dto.getNote() );
        engineServer.setCreateuser( dto.getCreateuser() );
        engineServer.setCreatetime( dto.getCreatetime() );
        engineServer.setUpdateuser( dto.getUpdateuser() );
        engineServer.setUpdatetime( dto.getUpdatetime() );
        engineServer.setSparefield1( dto.getSparefield1() );
        engineServer.setSparefield2( dto.getSparefield2() );
        engineServer.setSparefield3( dto.getSparefield3() );
        engineServer.setSparefield4( dto.getSparefield4() );
        engineServer.setSparefield5( dto.getSparefield5() );

        return engineServer;
    }

    @Override
    public EngineServerDto toDto(EngineServer entity) {
        if ( entity == null ) {
            return null;
        }

        EngineServerDto engineServerDto = new EngineServerDto();

        engineServerDto.setId( entity.getId() );
        engineServerDto.setName( entity.getName() );
        engineServerDto.setIp( entity.getIp() );
        engineServerDto.setPort( entity.getPort() );
        engineServerDto.setApplication( entity.getApplication() );
        engineServerDto.setIsvalid( entity.getIsvalid() );
        engineServerDto.setNote( entity.getNote() );
        engineServerDto.setCreateuser( entity.getCreateuser() );
        engineServerDto.setCreatetime( entity.getCreatetime() );
        engineServerDto.setUpdateuser( entity.getUpdateuser() );
        engineServerDto.setUpdatetime( entity.getUpdatetime() );
        engineServerDto.setSparefield1( entity.getSparefield1() );
        engineServerDto.setSparefield2( entity.getSparefield2() );
        engineServerDto.setSparefield3( entity.getSparefield3() );
        engineServerDto.setSparefield4( entity.getSparefield4() );
        engineServerDto.setSparefield5( entity.getSparefield5() );
        engineServerDto.setState( entity.getState() );

        return engineServerDto;
    }

    @Override
    public List<EngineServer> toEntity(List<EngineServerDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EngineServer> list = new ArrayList<EngineServer>( dtoList.size() );
        for ( EngineServerDto engineServerDto : dtoList ) {
            list.add( toEntity( engineServerDto ) );
        }

        return list;
    }

    @Override
    public List<EngineServerDto> toDto(List<EngineServer> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EngineServerDto> list = new ArrayList<EngineServerDto>( entityList.size() );
        for ( EngineServer engineServer : entityList ) {
            list.add( toDto( engineServer ) );
        }

        return list;
    }
}
