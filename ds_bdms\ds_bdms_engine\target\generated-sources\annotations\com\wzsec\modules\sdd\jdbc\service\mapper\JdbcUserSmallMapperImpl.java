package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcUser;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcUserSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class JdbcUserSmallMapperImpl implements JdbcUserSmallMapper {

    @Override
    public JdbcUser toEntity(JdbcUserSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcUser jdbcUser = new JdbcUser();

        jdbcUser.setId( dto.getId() );
        jdbcUser.setUsername( dto.getUsername() );
        jdbcUser.setNickName( dto.getNickName() );

        return jdbcUser;
    }

    @Override
    public JdbcUserSmallDto toDto(JdbcUser entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcUserSmallDto jdbcUserSmallDto = new JdbcUserSmallDto();

        jdbcUserSmallDto.setId( entity.getId() );
        jdbcUserSmallDto.setUsername( entity.getUsername() );
        jdbcUserSmallDto.setNickName( entity.getNickName() );

        return jdbcUserSmallDto;
    }

    @Override
    public List<JdbcUser> toEntity(List<JdbcUserSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcUser> list = new ArrayList<JdbcUser>( dtoList.size() );
        for ( JdbcUserSmallDto jdbcUserSmallDto : dtoList ) {
            list.add( toEntity( jdbcUserSmallDto ) );
        }

        return list;
    }

    @Override
    public List<JdbcUserSmallDto> toDto(List<JdbcUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcUserSmallDto> list = new ArrayList<JdbcUserSmallDto>( entityList.size() );
        for ( JdbcUser jdbcUser : entityList ) {
            list.add( toDto( jdbcUser ) );
        }

        return list;
    }
}
