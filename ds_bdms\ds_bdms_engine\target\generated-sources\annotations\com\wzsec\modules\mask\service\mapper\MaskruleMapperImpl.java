package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.Maskrule;
import com.wzsec.modules.mask.service.dto.MaskruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:23+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskruleMapperImpl implements MaskruleMapper {

    @Override
    public Maskrule toEntity(MaskruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        Maskrule maskrule = new Maskrule();

        maskrule.setId( dto.getId() );
        maskrule.setRulename( dto.getRulename() );
        maskrule.setRulecname( dto.getRulecname() );
        maskrule.setStandardcname( dto.getStandardcname() );
        maskrule.setStandardename( dto.getStandardename() );
        maskrule.setAlgorithmid( dto.getAlgorithmid() );
        maskrule.setParam( dto.getParam() );
        maskrule.setFlag( dto.getFlag() );
        maskrule.setMemo( dto.getMemo() );
        maskrule.setCreateuser( dto.getCreateuser() );
        maskrule.setCreatetime( dto.getCreatetime() );
        maskrule.setUpdateuser( dto.getUpdateuser() );
        maskrule.setUpdatetime( dto.getUpdatetime() );
        maskrule.setSparefield1( dto.getSparefield1() );
        maskrule.setSparefield2( dto.getSparefield2() );
        maskrule.setSparefield3( dto.getSparefield3() );
        maskrule.setSparefield4( dto.getSparefield4() );
        maskrule.setSparefield5( dto.getSparefield5() );

        return maskrule;
    }

    @Override
    public MaskruleDto toDto(Maskrule entity) {
        if ( entity == null ) {
            return null;
        }

        MaskruleDto maskruleDto = new MaskruleDto();

        maskruleDto.setId( entity.getId() );
        maskruleDto.setRulename( entity.getRulename() );
        maskruleDto.setRulecname( entity.getRulecname() );
        maskruleDto.setStandardcname( entity.getStandardcname() );
        maskruleDto.setStandardename( entity.getStandardename() );
        maskruleDto.setAlgorithmid( entity.getAlgorithmid() );
        maskruleDto.setParam( entity.getParam() );
        maskruleDto.setFlag( entity.getFlag() );
        maskruleDto.setMemo( entity.getMemo() );
        maskruleDto.setCreateuser( entity.getCreateuser() );
        maskruleDto.setCreatetime( entity.getCreatetime() );
        maskruleDto.setUpdateuser( entity.getUpdateuser() );
        maskruleDto.setUpdatetime( entity.getUpdatetime() );
        maskruleDto.setSparefield1( entity.getSparefield1() );
        maskruleDto.setSparefield2( entity.getSparefield2() );
        maskruleDto.setSparefield3( entity.getSparefield3() );
        maskruleDto.setSparefield4( entity.getSparefield4() );
        maskruleDto.setSparefield5( entity.getSparefield5() );

        return maskruleDto;
    }

    @Override
    public List<Maskrule> toEntity(List<MaskruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Maskrule> list = new ArrayList<Maskrule>( dtoList.size() );
        for ( MaskruleDto maskruleDto : dtoList ) {
            list.add( toEntity( maskruleDto ) );
        }

        return list;
    }

    @Override
    public List<MaskruleDto> toDto(List<Maskrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskruleDto> list = new ArrayList<MaskruleDto>( entityList.size() );
        for ( Maskrule maskrule : entityList ) {
            list.add( toDto( maskrule ) );
        }

        return list;
    }
}
