package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.ResultRule;
import com.wzsec.modules.proxy.service.dto.ResultRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ResultRuleMapperImpl implements ResultRuleMapper {

    @Override
    public ResultRule toEntity(ResultRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultRule resultRule = new ResultRule();

        resultRule.setId( dto.getId() );
        resultRule.setSname( dto.getSname() );
        resultRule.setCheckrule( dto.getCheckrule() );
        resultRule.setAlgorithm( dto.getAlgorithm() );
        resultRule.setParam( dto.getParam() );
        resultRule.setDatasource( dto.getDatasource() );
        resultRule.setRole( dto.getRole() );
        resultRule.setState( dto.getState() );
        resultRule.setCategory( dto.getCategory() );
        resultRule.setLevel( dto.getLevel() );
        resultRule.setCreateuser( dto.getCreateuser() );
        resultRule.setCreatetime( dto.getCreatetime() );
        resultRule.setUpdateuser( dto.getUpdateuser() );
        resultRule.setUpdatetime( dto.getUpdatetime() );
        resultRule.setNote( dto.getNote() );
        resultRule.setSparefield1( dto.getSparefield1() );
        resultRule.setSparefield2( dto.getSparefield2() );
        resultRule.setSparefield3( dto.getSparefield3() );
        resultRule.setSparefield4( dto.getSparefield4() );
        resultRule.setSparefield5( dto.getSparefield5() );

        return resultRule;
    }

    @Override
    public ResultRuleDto toDto(ResultRule entity) {
        if ( entity == null ) {
            return null;
        }

        ResultRuleDto resultRuleDto = new ResultRuleDto();

        resultRuleDto.setId( entity.getId() );
        resultRuleDto.setSname( entity.getSname() );
        resultRuleDto.setCheckrule( entity.getCheckrule() );
        resultRuleDto.setAlgorithm( entity.getAlgorithm() );
        resultRuleDto.setParam( entity.getParam() );
        resultRuleDto.setDatasource( entity.getDatasource() );
        resultRuleDto.setRole( entity.getRole() );
        resultRuleDto.setState( entity.getState() );
        resultRuleDto.setCategory( entity.getCategory() );
        resultRuleDto.setLevel( entity.getLevel() );
        resultRuleDto.setCreateuser( entity.getCreateuser() );
        resultRuleDto.setCreatetime( entity.getCreatetime() );
        resultRuleDto.setUpdateuser( entity.getUpdateuser() );
        resultRuleDto.setUpdatetime( entity.getUpdatetime() );
        resultRuleDto.setNote( entity.getNote() );
        resultRuleDto.setSparefield1( entity.getSparefield1() );
        resultRuleDto.setSparefield2( entity.getSparefield2() );
        resultRuleDto.setSparefield3( entity.getSparefield3() );
        resultRuleDto.setSparefield4( entity.getSparefield4() );
        resultRuleDto.setSparefield5( entity.getSparefield5() );

        return resultRuleDto;
    }

    @Override
    public List<ResultRule> toEntity(List<ResultRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultRule> list = new ArrayList<ResultRule>( dtoList.size() );
        for ( ResultRuleDto resultRuleDto : dtoList ) {
            list.add( toEntity( resultRuleDto ) );
        }

        return list;
    }

    @Override
    public List<ResultRuleDto> toDto(List<ResultRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultRuleDto> list = new ArrayList<ResultRuleDto>( entityList.size() );
        for ( ResultRule resultRule : entityList ) {
            list.add( toDto( resultRule ) );
        }

        return list;
    }
}
