package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.BlackWhiteList;
import com.wzsec.modules.proxy.service.dto.BlackWhiteListDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class BlackWhiteListMapperImpl implements BlackWhiteListMapper {

    @Override
    public BlackWhiteList toEntity(BlackWhiteListDto dto) {
        if ( dto == null ) {
            return null;
        }

        BlackWhiteList blackWhiteList = new BlackWhiteList();

        blackWhiteList.setId( dto.getId() );
        blackWhiteList.setSrcid( dto.getSrcid() );
        blackWhiteList.setContent( dto.getContent() );
        blackWhiteList.setType( dto.getType() );
        blackWhiteList.setPurpose( dto.getPurpose() );
        blackWhiteList.setState( dto.getState() );
        blackWhiteList.setCreateuser( dto.getCreateuser() );
        blackWhiteList.setCreatetime( dto.getCreatetime() );
        blackWhiteList.setUpdateuser( dto.getUpdateuser() );
        blackWhiteList.setUpdatetime( dto.getUpdatetime() );
        blackWhiteList.setMemo( dto.getMemo() );
        blackWhiteList.setSparefield1( dto.getSparefield1() );
        blackWhiteList.setSparefield2( dto.getSparefield2() );
        blackWhiteList.setSparefield3( dto.getSparefield3() );
        blackWhiteList.setSparefield4( dto.getSparefield4() );
        blackWhiteList.setSparefield5( dto.getSparefield5() );

        return blackWhiteList;
    }

    @Override
    public BlackWhiteListDto toDto(BlackWhiteList entity) {
        if ( entity == null ) {
            return null;
        }

        BlackWhiteListDto blackWhiteListDto = new BlackWhiteListDto();

        blackWhiteListDto.setId( entity.getId() );
        blackWhiteListDto.setSrcid( entity.getSrcid() );
        blackWhiteListDto.setContent( entity.getContent() );
        blackWhiteListDto.setType( entity.getType() );
        blackWhiteListDto.setPurpose( entity.getPurpose() );
        blackWhiteListDto.setState( entity.getState() );
        blackWhiteListDto.setCreateuser( entity.getCreateuser() );
        blackWhiteListDto.setCreatetime( entity.getCreatetime() );
        blackWhiteListDto.setUpdateuser( entity.getUpdateuser() );
        blackWhiteListDto.setUpdatetime( entity.getUpdatetime() );
        blackWhiteListDto.setMemo( entity.getMemo() );
        blackWhiteListDto.setSparefield1( entity.getSparefield1() );
        blackWhiteListDto.setSparefield2( entity.getSparefield2() );
        blackWhiteListDto.setSparefield3( entity.getSparefield3() );
        blackWhiteListDto.setSparefield4( entity.getSparefield4() );
        blackWhiteListDto.setSparefield5( entity.getSparefield5() );

        return blackWhiteListDto;
    }

    @Override
    public List<BlackWhiteList> toEntity(List<BlackWhiteListDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<BlackWhiteList> list = new ArrayList<BlackWhiteList>( dtoList.size() );
        for ( BlackWhiteListDto blackWhiteListDto : dtoList ) {
            list.add( toEntity( blackWhiteListDto ) );
        }

        return list;
    }

    @Override
    public List<BlackWhiteListDto> toDto(List<BlackWhiteList> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BlackWhiteListDto> list = new ArrayList<BlackWhiteListDto>( entityList.size() );
        for ( BlackWhiteList blackWhiteList : entityList ) {
            list.add( toDto( blackWhiteList ) );
        }

        return list;
    }
}
