<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <contextName>ds_bdms_system</contextName>
    <property name="log.charset" value="utf-8" />
    <property name="log.pattern" value="%(%contextName-) %red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}:%line\) - %(%msg%n)" />

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志存储位置配置 -->
            <fileNamePattern>./logs/ds_bdms_system.%d.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%d %p (%file:%line\)- %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="SYSLOG" class="ch.qos.logback.classic.net.SyslogAppender">
        <!--替换为syslog地址-->
        <syslogHost>127.0.0.1</syslogHost>
        <port>514</port>
        <facility>LOCAL1</facility>
        <suffixPattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|${appName}|${env}|%-5level|%thread|%C:%M:%L| - %msg</suffixPattern>
    </appender>

    <!--普通日志输出到控制台-->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="file" />
        <!--不使用syslog时，不要启用-->
        <!--<appender-ref ref="SYSLOG" />-->
    </root>
    
    <!--监控sql日志输出 -->
    <logger name="jdbc.sqlonly" level="INFO" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <logger name="jdbc.resultset" level="ERROR" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <!--  如想看到表格数据，将OFF改为INFO  -->
    <logger name="jdbc.resultsettable" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <logger name="jdbc.connection" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <logger name="jdbc.sqltiming" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>

    <logger name="jdbc.audit" level="OFF" additivity="false">
        <appender-ref ref="console" />
    </logger>
</configuration>