package com.wzsec.dotask.mask.service.impl;

import com.wzsec.dotask.mask.service.DoDBTaskService;
import com.wzsec.dotask.mask.service.excute.db.DoDBTaskJob_V2;
import com.wzsec.modules.alarm.service.DmAlarmdisposalService;
import com.wzsec.modules.mask.domain.EngineServer;
import com.wzsec.modules.mask.domain.TaskProgressModel;
import com.wzsec.modules.mask.repository.EngineServerRepository;
import com.wzsec.modules.mask.service.*;
import com.wzsec.modules.mask.service.dto.DBTaskConfigDto;
import com.wzsec.modules.mask.service.mapper.DbtaskconfigMapper;
import com.wzsec.modules.mask.service.mapper.DbtaskresultMapper;
import com.wzsec.modules.sdd.metadata.service.MetaFieldService;
import com.wzsec.modules.sdd.sdk.repository.SdkApplyconfigRepository;
import com.wzsec.modules.sdd.sdk.repository.SdkOperationrecordRepository;
import com.wzsec.modules.sdd.source.service.DatasourceService;
import com.wzsec.modules.statistics.service.MaskTaskresultrecordsService;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.utils.Const;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-12
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "wpTask")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DoDBTaskServiceImpl implements DoDBTaskService {

    private final DBTaskResultService dBTaskResultService;

    private final DBTaskConfigService dbTaskConfigService;

    private final DatasourceService dataSourceService;

    private final MaskStrategyFieldService maskStrategyFieldService;

    private MaskStrategyTableService maskStrategyTableService;

    private final AlgorithmService algorithmService;

    private DbtaskconfigMapper dbtaskconfigMapper;

    private DbtaskresultMapper dbtaskresultMapper;

    private final UserService userService;

    public static Map<String, String> idNameMap = new HashMap<String, String>();

    private TaskProgressModel taskProgressModel;

    private final DmAlarmdisposalService dmAlarmdisposalService;

    private final SystemLogService logService;

    private final MaskTaskresultrecordsService maskTaskresultrecordsService;

    private final MetaFieldService metaFieldService;

    private final EngineServerRepository engineServerRepository;

    private final SdkOperationrecordRepository sdkOperationrecordRepository;

    private final SdkApplyconfigRepository sdkApplyconfigRepository;

    @Value("${spring.profiles.active}")
    private String active;


    public DoDBTaskServiceImpl(DBTaskConfigService dbTaskConfigService, DatasourceService datasourceService,
                               MaskStrategyTableService maskStrategyTableService,MaskStrategyFieldService maskStrategyFieldService,
                               DBTaskResultService dBTaskResultService, UserService userService, AlgorithmService algorithmService,
                               DbtaskconfigMapper dbtaskconfigMapper, DbtaskresultMapper dbtaskresultMapper, DmAlarmdisposalService dmAlarmdisposalService,
                               SystemLogService logService,MaskTaskresultrecordsService maskTaskresultrecordsService, MetaFieldService metaFieldService,
                               EngineServerRepository engineServerRepository,SdkApplyconfigRepository sdkApplyconfigRepository,SdkOperationrecordRepository sdkOperationrecordRepository) {
        this.dbTaskConfigService = dbTaskConfigService;
        this.dataSourceService = datasourceService;
        this.maskStrategyFieldService = maskStrategyFieldService;
        this.maskStrategyTableService = maskStrategyTableService;
        this.dBTaskResultService = dBTaskResultService;
        this.userService = userService;
        this.algorithmService = algorithmService;
        this.dbtaskconfigMapper = dbtaskconfigMapper;
        this.dbtaskresultMapper = dbtaskresultMapper;
        this.dmAlarmdisposalService = dmAlarmdisposalService;
        this.logService = logService;
        this.maskTaskresultrecordsService = maskTaskresultrecordsService;
        this.metaFieldService = metaFieldService;
        this.engineServerRepository = engineServerRepository;
        this.sdkApplyconfigRepository = sdkApplyconfigRepository;
        this.sdkOperationrecordRepository = sdkOperationrecordRepository;
    }

    @Async//异步执行
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execution(Integer id, String submituser) {
        // 分批加载数据不支持使用v1
//        DoDBTaskJob_V1 doDBTaskJob = new DoDBTaskJob_V1(dbtaskconfigService, dataSourceService,
//                maskStrategyFieldService, dbtaskresultService,
//                algorithmService, dbtaskconfigMapper, dbtaskresultMapper,
//                id, submituser);
        //分页查询慢使用v2

        Thread t = Thread.currentThread();
        String name = t.getName();
        System.out.println("dbmasktask_name" + name);
        idNameMap.put(id + "", name);
        System.out.println(id);
        log.info("任务ID【" + id + "】,执行脱敏任务开始");

        performTasks(id, submituser);

    }

    /**
     * 执行脱敏任务
     *
     * @param id         id
     * @param submituser submituser
     */
    public void performTasks(Integer id, String submituser) {

        DBTaskConfigDto configServiceById = dbTaskConfigService.findById(id);
        String engine = configServiceById.getSparefield4();
        String[] ipPort = engine.split("-")[1].trim().split(":");
        String ip = ipPort[0];
        String port = ipPort[1];

        //TODO 将所使用的引擎，任务负载数+1
        if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
            engineServerRepository.addKingbaseCountByIpPort(ip, port);
        }else{
            engineServerRepository.addCountByIpPort(ip, port);
        }

        DoDBTaskJob_V2 doDBTaskJob = new DoDBTaskJob_V2(dbTaskConfigService, dataSourceService,
                maskStrategyFieldService,maskStrategyTableService,  dBTaskResultService, userService,
                algorithmService, dbtaskconfigMapper, dbtaskresultMapper,
                taskProgressModel, id, submituser,dmAlarmdisposalService,logService,maskTaskresultrecordsService,metaFieldService,sdkOperationrecordRepository,sdkApplyconfigRepository);
        doDBTaskJob.exec(ip,port);
        //执行成功即删除线程
        idNameMap.remove(id + "");

        //TODO 将所使用的引擎，任务负载数-1
        if (Const.DB_KINGBASE8.equalsIgnoreCase(active)){
            engineServerRepository.reduceKingbaseCountByIpPort(ip, port);
        }else {
            engineServerRepository.reduceCountByIpPort(ip, port);
        }
    }


    /**
     * 操作指令
     *
     * @param id  id
     * @param cid cid
     */
    @Async
    @Override
    public void operationInstructions(Integer id, Integer cid) {
        String result = "false";
        try {
            ThreadGroup currentGroup = Thread.currentThread().getThreadGroup();
            int noThreads = currentGroup.activeCount();
            Thread[] lstThreads = new Thread[noThreads];
            currentGroup.enumerate(lstThreads);
            System.out.println("现有线程数" + noThreads);
            String name = idNameMap.get(id + "");
            System.out.println(name);

            DBTaskConfigDto dbTaskConfig = dbTaskConfigService.findById(id);
            String taskname = dbTaskConfig.getTaskname();

            for (int i = 0; i < noThreads; i++) {
                // sign(1暂停,2恢复,3停止)
                String nm = lstThreads[i].getName();
                // System.out.println("线程号：" + i + " = " + nm);
                if (nm.equals(name)) {
                    if (cid == 1) {
                        System.out.println("暂停" + nm);
                        lstThreads[i].suspend();
                        dbTaskConfigService.updateTaskStatus(id, Const.TASK_STATUS_SUSPEND); //更新任务表手动暂停
                        dBTaskResultService.updateTaskStatusByTaskName(taskname, Const.TASK_RESULT_EXECUTING); //更新结果执行中
                    } else if (cid == 2) {
                        System.out.println("恢复" + nm);
                        lstThreads[i].resume();
                        dBTaskResultService.updateTaskStatusByTaskName(taskname, Const.TASK_RESULT_EXECUTING); //更新结果执行中
                    } else if (cid == 3) {
                        System.out.println("kill" + nm);
                        lstThreads[i].stop();
                        dbTaskConfigService.updateTaskStatus(id, Const.TASK_STATUS_STOP); //更新任务表手动暂停
                    }
                    result = "true";
                    break;
                }
            }
            if (!idNameMap.containsKey(id + "")) {// 说明任务执行完毕
                dbTaskConfigService.updateTaskStatus(id, Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS); //更新任务执行成功
                dBTaskResultService.updateTaskStatusByTaskName(taskname, Const.TASK_RESULT_EXECUTE_SUCCESS); //更新结果执行成功
            }
            log.info("调用接口操作指令成功");
        } catch (Exception e) {
            log.error("调用接口操作指令出现异常");
        }
    }


}
