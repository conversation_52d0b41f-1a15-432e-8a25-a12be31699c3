package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.KafkaTaskConfig;
import com.wzsec.modules.mask.service.dto.KafkaTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class KafkaTaskConfigMapperImpl implements KafkaTaskConfigMapper {

    @Override
    public KafkaTaskConfig toEntity(KafkaTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        KafkaTaskConfig kafkaTaskConfig = new KafkaTaskConfig();

        kafkaTaskConfig.setId( dto.getId() );
        kafkaTaskConfig.setTaskname( dto.getTaskname() );
        kafkaTaskConfig.setDbname( dto.getDbname() );
        kafkaTaskConfig.setTbname( dto.getTbname() );
        kafkaTaskConfig.setRediskey( dto.getRediskey() );
        kafkaTaskConfig.setStrategyid( dto.getStrategyid() );
        kafkaTaskConfig.setDatatopic( dto.getDatatopic() );
        kafkaTaskConfig.setDatatype( dto.getDatatype() );
        kafkaTaskConfig.setIsvalid( dto.getIsvalid() );
        kafkaTaskConfig.setStatus( dto.getStatus() );
        kafkaTaskConfig.setCreateuser( dto.getCreateuser() );
        kafkaTaskConfig.setCreatetime( dto.getCreatetime() );
        kafkaTaskConfig.setUpdateuser( dto.getUpdateuser() );
        kafkaTaskConfig.setUpdatetime( dto.getUpdatetime() );
        kafkaTaskConfig.setNote( dto.getNote() );
        kafkaTaskConfig.setSparefield1( dto.getSparefield1() );
        kafkaTaskConfig.setSparefield2( dto.getSparefield2() );
        kafkaTaskConfig.setSparefield3( dto.getSparefield3() );
        kafkaTaskConfig.setSparefield4( dto.getSparefield4() );

        return kafkaTaskConfig;
    }

    @Override
    public KafkaTaskConfigDto toDto(KafkaTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        KafkaTaskConfigDto kafkaTaskConfigDto = new KafkaTaskConfigDto();

        kafkaTaskConfigDto.setId( entity.getId() );
        kafkaTaskConfigDto.setTaskname( entity.getTaskname() );
        kafkaTaskConfigDto.setDbname( entity.getDbname() );
        kafkaTaskConfigDto.setTbname( entity.getTbname() );
        kafkaTaskConfigDto.setRediskey( entity.getRediskey() );
        kafkaTaskConfigDto.setStrategyid( entity.getStrategyid() );
        kafkaTaskConfigDto.setDatatopic( entity.getDatatopic() );
        kafkaTaskConfigDto.setDatatype( entity.getDatatype() );
        kafkaTaskConfigDto.setIsvalid( entity.getIsvalid() );
        kafkaTaskConfigDto.setStatus( entity.getStatus() );
        kafkaTaskConfigDto.setCreateuser( entity.getCreateuser() );
        kafkaTaskConfigDto.setCreatetime( entity.getCreatetime() );
        kafkaTaskConfigDto.setUpdateuser( entity.getUpdateuser() );
        kafkaTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        kafkaTaskConfigDto.setNote( entity.getNote() );
        kafkaTaskConfigDto.setSparefield1( entity.getSparefield1() );
        kafkaTaskConfigDto.setSparefield2( entity.getSparefield2() );
        kafkaTaskConfigDto.setSparefield3( entity.getSparefield3() );
        kafkaTaskConfigDto.setSparefield4( entity.getSparefield4() );

        return kafkaTaskConfigDto;
    }

    @Override
    public List<KafkaTaskConfig> toEntity(List<KafkaTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KafkaTaskConfig> list = new ArrayList<KafkaTaskConfig>( dtoList.size() );
        for ( KafkaTaskConfigDto kafkaTaskConfigDto : dtoList ) {
            list.add( toEntity( kafkaTaskConfigDto ) );
        }

        return list;
    }

    @Override
    public List<KafkaTaskConfigDto> toDto(List<KafkaTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KafkaTaskConfigDto> list = new ArrayList<KafkaTaskConfigDto>( entityList.size() );
        for ( KafkaTaskConfig kafkaTaskConfig : entityList ) {
            list.add( toDto( kafkaTaskConfig ) );
        }

        return list;
    }
}
