package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditLogResultdetail;
import com.wzsec.modules.mask.service.dto.MaskAuditLogResultdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAuditLogResultdetailMapperImpl implements MaskAuditLogResultdetailMapper {

    @Override
    public MaskAuditLogResultdetail toEntity(MaskAuditLogResultdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditLogResultdetail maskAuditLogResultdetail = new MaskAuditLogResultdetail();

        maskAuditLogResultdetail.setId( dto.getId() );
        maskAuditLogResultdetail.setTaskname( dto.getTaskname() );
        maskAuditLogResultdetail.setCustname( dto.getCustname() );
        maskAuditLogResultdetail.setCustSimplename( dto.getCustSimplename() );
        maskAuditLogResultdetail.setUserid( dto.getUserid() );
        maskAuditLogResultdetail.setAppid( dto.getAppid() );
        maskAuditLogResultdetail.setAppname( dto.getAppname() );
        maskAuditLogResultdetail.setLogsign( dto.getLogsign() );
        maskAuditLogResultdetail.setApicode( dto.getApicode() );
        maskAuditLogResultdetail.setApiname( dto.getApiname() );
        maskAuditLogResultdetail.setApimethod( dto.getApimethod() );
        maskAuditLogResultdetail.setApitype( dto.getApitype() );
        maskAuditLogResultdetail.setUrl( dto.getUrl() );
        maskAuditLogResultdetail.setCheckparam( dto.getCheckparam() );
        maskAuditLogResultdetail.setParamMean( dto.getParamMean() );
        maskAuditLogResultdetail.setParamNote( dto.getParamNote() );
        maskAuditLogResultdetail.setCheckcount( dto.getCheckcount() );
        maskAuditLogResultdetail.setTotalcount( dto.getTotalcount() );
        maskAuditLogResultdetail.setChecklinescount( dto.getChecklinescount() );
        maskAuditLogResultdetail.setTotallinescount( dto.getTotallinescount() );
        maskAuditLogResultdetail.setChecktime( dto.getChecktime() );
        maskAuditLogResultdetail.setRatio( dto.getRatio() );
        maskAuditLogResultdetail.setResulttype( dto.getResulttype() );
        maskAuditLogResultdetail.setRisk( dto.getRisk() );
        maskAuditLogResultdetail.setSensitivedata( dto.getSensitivedata() );
        maskAuditLogResultdetail.setExample( dto.getExample() );
        maskAuditLogResultdetail.setSparefield1( dto.getSparefield1() );
        maskAuditLogResultdetail.setSparefield2( dto.getSparefield2() );
        maskAuditLogResultdetail.setSparefield3( dto.getSparefield3() );
        maskAuditLogResultdetail.setSparefield4( dto.getSparefield4() );

        return maskAuditLogResultdetail;
    }

    @Override
    public MaskAuditLogResultdetailDto toDto(MaskAuditLogResultdetail entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditLogResultdetailDto maskAuditLogResultdetailDto = new MaskAuditLogResultdetailDto();

        maskAuditLogResultdetailDto.setId( entity.getId() );
        maskAuditLogResultdetailDto.setTaskname( entity.getTaskname() );
        maskAuditLogResultdetailDto.setCustname( entity.getCustname() );
        maskAuditLogResultdetailDto.setCustSimplename( entity.getCustSimplename() );
        maskAuditLogResultdetailDto.setUserid( entity.getUserid() );
        maskAuditLogResultdetailDto.setAppid( entity.getAppid() );
        maskAuditLogResultdetailDto.setAppname( entity.getAppname() );
        maskAuditLogResultdetailDto.setLogsign( entity.getLogsign() );
        maskAuditLogResultdetailDto.setApicode( entity.getApicode() );
        maskAuditLogResultdetailDto.setApiname( entity.getApiname() );
        maskAuditLogResultdetailDto.setApimethod( entity.getApimethod() );
        maskAuditLogResultdetailDto.setApitype( entity.getApitype() );
        maskAuditLogResultdetailDto.setUrl( entity.getUrl() );
        maskAuditLogResultdetailDto.setCheckparam( entity.getCheckparam() );
        maskAuditLogResultdetailDto.setParamMean( entity.getParamMean() );
        maskAuditLogResultdetailDto.setParamNote( entity.getParamNote() );
        maskAuditLogResultdetailDto.setCheckcount( entity.getCheckcount() );
        maskAuditLogResultdetailDto.setTotalcount( entity.getTotalcount() );
        maskAuditLogResultdetailDto.setChecklinescount( entity.getChecklinescount() );
        maskAuditLogResultdetailDto.setTotallinescount( entity.getTotallinescount() );
        maskAuditLogResultdetailDto.setChecktime( entity.getChecktime() );
        maskAuditLogResultdetailDto.setRatio( entity.getRatio() );
        maskAuditLogResultdetailDto.setResulttype( entity.getResulttype() );
        maskAuditLogResultdetailDto.setRisk( entity.getRisk() );
        maskAuditLogResultdetailDto.setSensitivedata( entity.getSensitivedata() );
        maskAuditLogResultdetailDto.setExample( entity.getExample() );
        maskAuditLogResultdetailDto.setSparefield1( entity.getSparefield1() );
        maskAuditLogResultdetailDto.setSparefield2( entity.getSparefield2() );
        maskAuditLogResultdetailDto.setSparefield3( entity.getSparefield3() );
        maskAuditLogResultdetailDto.setSparefield4( entity.getSparefield4() );

        return maskAuditLogResultdetailDto;
    }

    @Override
    public List<MaskAuditLogResultdetail> toEntity(List<MaskAuditLogResultdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditLogResultdetail> list = new ArrayList<MaskAuditLogResultdetail>( dtoList.size() );
        for ( MaskAuditLogResultdetailDto maskAuditLogResultdetailDto : dtoList ) {
            list.add( toEntity( maskAuditLogResultdetailDto ) );
        }

        return list;
    }

    @Override
    public List<MaskAuditLogResultdetailDto> toDto(List<MaskAuditLogResultdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditLogResultdetailDto> list = new ArrayList<MaskAuditLogResultdetailDto>( entityList.size() );
        for ( MaskAuditLogResultdetail maskAuditLogResultdetail : entityList ) {
            list.add( toDto( maskAuditLogResultdetail ) );
        }

        return list;
    }
}
