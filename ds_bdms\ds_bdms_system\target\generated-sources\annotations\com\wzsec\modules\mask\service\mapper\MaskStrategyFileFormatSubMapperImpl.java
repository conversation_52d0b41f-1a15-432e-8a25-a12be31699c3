package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyFileFormatSub;
import com.wzsec.modules.mask.service.dto.MaskStrategyFileFormatSubDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyFileFormatSubMapperImpl implements MaskStrategyFileFormatSubMapper {

    @Override
    public MaskStrategyFileFormatSub toEntity(MaskStrategyFileFormatSubDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyFileFormatSub maskStrategyFileFormatSub = new MaskStrategyFileFormatSub();

        maskStrategyFileFormatSub.setId( dto.getId() );
        maskStrategyFileFormatSub.setStrategyid( dto.getStrategyid() );
        maskStrategyFileFormatSub.setColumnnum( dto.getColumnnum() );
        maskStrategyFileFormatSub.setColumndesc( dto.getColumndesc() );
        maskStrategyFileFormatSub.setSenLevel( dto.getSenLevel() );
        maskStrategyFileFormatSub.setRuleid( dto.getRuleid() );
        maskStrategyFileFormatSub.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyFileFormatSub.setParam( dto.getParam() );
        maskStrategyFileFormatSub.setSecretkey( dto.getSecretkey() );
        maskStrategyFileFormatSub.setSparefield1( dto.getSparefield1() );
        maskStrategyFileFormatSub.setSparefield2( dto.getSparefield2() );
        maskStrategyFileFormatSub.setSparefield3( dto.getSparefield3() );
        maskStrategyFileFormatSub.setSparefield4( dto.getSparefield4() );

        return maskStrategyFileFormatSub;
    }

    @Override
    public MaskStrategyFileFormatSubDto toDto(MaskStrategyFileFormatSub entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFileFormatSubDto maskStrategyFileFormatSubDto = new MaskStrategyFileFormatSubDto();

        maskStrategyFileFormatSubDto.setId( entity.getId() );
        maskStrategyFileFormatSubDto.setStrategyid( entity.getStrategyid() );
        maskStrategyFileFormatSubDto.setColumnnum( entity.getColumnnum() );
        maskStrategyFileFormatSubDto.setColumndesc( entity.getColumndesc() );
        maskStrategyFileFormatSubDto.setSenLevel( entity.getSenLevel() );
        maskStrategyFileFormatSubDto.setRuleid( entity.getRuleid() );
        maskStrategyFileFormatSubDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyFileFormatSubDto.setParam( entity.getParam() );
        maskStrategyFileFormatSubDto.setSecretkey( entity.getSecretkey() );
        maskStrategyFileFormatSubDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFileFormatSubDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFileFormatSubDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFileFormatSubDto.setSparefield4( entity.getSparefield4() );

        return maskStrategyFileFormatSubDto;
    }

    @Override
    public List<MaskStrategyFileFormatSub> toEntity(List<MaskStrategyFileFormatSubDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyFileFormatSub> list = new ArrayList<MaskStrategyFileFormatSub>( dtoList.size() );
        for ( MaskStrategyFileFormatSubDto maskStrategyFileFormatSubDto : dtoList ) {
            list.add( toEntity( maskStrategyFileFormatSubDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyFileFormatSubDto> toDto(List<MaskStrategyFileFormatSub> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFileFormatSubDto> list = new ArrayList<MaskStrategyFileFormatSubDto>( entityList.size() );
        for ( MaskStrategyFileFormatSub maskStrategyFileFormatSub : entityList ) {
            list.add( toDto( maskStrategyFileFormatSub ) );
        }

        return list;
    }
}
