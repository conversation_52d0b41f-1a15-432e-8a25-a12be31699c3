package com.wzsec.modules.sdd.category.service.mapper;

import com.wzsec.modules.sdd.category.domain.Category;
import com.wzsec.modules.sdd.category.service.dto.CategoryDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class CategoryMapperImpl implements CategoryMapper {

    @Override
    public Category toEntity(CategoryDto dto) {
        if ( dto == null ) {
            return null;
        }

        Category category = new Category();

        category.setId( dto.getId() );
        category.setCategory( dto.getCategory() );
        category.setCategoryname( dto.getCategoryname() );
        category.setType( dto.getType() );
        category.setPid( dto.getPid() );
        category.setRuleid( dto.getRuleid() );
        category.setData( dto.getData() );
        category.setExample( dto.getExample() );
        category.setCreateuser( dto.getCreateuser() );
        category.setCreatetime( dto.getCreatetime() );
        category.setUpdateuser( dto.getUpdateuser() );
        category.setUpdatetime( dto.getUpdatetime() );
        category.setSparefield1( dto.getSparefield1() );
        category.setSparefield2( dto.getSparefield2() );
        category.setSparefield3( dto.getSparefield3() );
        category.setSparefield4( dto.getSparefield4() );
        category.setSparefield5( dto.getSparefield5() );

        return category;
    }

    @Override
    public CategoryDto toDto(Category entity) {
        if ( entity == null ) {
            return null;
        }

        CategoryDto categoryDto = new CategoryDto();

        categoryDto.setId( entity.getId() );
        categoryDto.setCategory( entity.getCategory() );
        categoryDto.setCategoryname( entity.getCategoryname() );
        categoryDto.setType( entity.getType() );
        categoryDto.setPid( entity.getPid() );
        categoryDto.setRuleid( entity.getRuleid() );
        categoryDto.setData( entity.getData() );
        categoryDto.setExample( entity.getExample() );
        categoryDto.setCreateuser( entity.getCreateuser() );
        categoryDto.setCreatetime( entity.getCreatetime() );
        categoryDto.setUpdateuser( entity.getUpdateuser() );
        categoryDto.setUpdatetime( entity.getUpdatetime() );
        categoryDto.setSparefield1( entity.getSparefield1() );
        categoryDto.setSparefield2( entity.getSparefield2() );
        categoryDto.setSparefield3( entity.getSparefield3() );
        categoryDto.setSparefield4( entity.getSparefield4() );
        categoryDto.setSparefield5( entity.getSparefield5() );

        return categoryDto;
    }

    @Override
    public List<Category> toEntity(List<CategoryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Category> list = new ArrayList<Category>( dtoList.size() );
        for ( CategoryDto categoryDto : dtoList ) {
            list.add( toEntity( categoryDto ) );
        }

        return list;
    }

    @Override
    public List<CategoryDto> toDto(List<Category> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<CategoryDto> list = new ArrayList<CategoryDto>( entityList.size() );
        for ( Category category : entityList ) {
            list.add( toDto( category ) );
        }

        return list;
    }
}
