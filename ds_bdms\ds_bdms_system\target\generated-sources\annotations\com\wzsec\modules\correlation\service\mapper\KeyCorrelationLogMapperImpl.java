package com.wzsec.modules.correlation.service.mapper;

import com.wzsec.modules.correlation.domain.KeyCorrelationLog;
import com.wzsec.modules.correlation.service.dto.KeyCorrelationLogDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class KeyCorrelationLogMapperImpl implements KeyCorrelationLogMapper {

    @Override
    public KeyCorrelationLog toEntity(KeyCorrelationLogDto dto) {
        if ( dto == null ) {
            return null;
        }

        KeyCorrelationLog keyCorrelationLog = new KeyCorrelationLog();

        keyCorrelationLog.setId( dto.getId() );
        keyCorrelationLog.setOperatetype( dto.getOperatetype() );
        keyCorrelationLog.setTabletype( dto.getTabletype() );
        keyCorrelationLog.setTablename( dto.getTablename() );
        keyCorrelationLog.setTablecname( dto.getTablecname() );
        keyCorrelationLog.setFieldename( dto.getFieldename() );
        keyCorrelationLog.setFieldcname( dto.getFieldcname() );
        keyCorrelationLog.setDetails( dto.getDetails() );
        keyCorrelationLog.setOperateuser( dto.getOperateuser() );
        keyCorrelationLog.setOperatetime( dto.getOperatetime() );

        return keyCorrelationLog;
    }

    @Override
    public KeyCorrelationLogDto toDto(KeyCorrelationLog entity) {
        if ( entity == null ) {
            return null;
        }

        KeyCorrelationLogDto keyCorrelationLogDto = new KeyCorrelationLogDto();

        keyCorrelationLogDto.setId( entity.getId() );
        keyCorrelationLogDto.setOperatetype( entity.getOperatetype() );
        keyCorrelationLogDto.setTabletype( entity.getTabletype() );
        keyCorrelationLogDto.setTablename( entity.getTablename() );
        keyCorrelationLogDto.setTablecname( entity.getTablecname() );
        keyCorrelationLogDto.setFieldename( entity.getFieldename() );
        keyCorrelationLogDto.setFieldcname( entity.getFieldcname() );
        keyCorrelationLogDto.setDetails( entity.getDetails() );
        keyCorrelationLogDto.setOperateuser( entity.getOperateuser() );
        keyCorrelationLogDto.setOperatetime( entity.getOperatetime() );

        return keyCorrelationLogDto;
    }

    @Override
    public List<KeyCorrelationLog> toEntity(List<KeyCorrelationLogDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KeyCorrelationLog> list = new ArrayList<KeyCorrelationLog>( dtoList.size() );
        for ( KeyCorrelationLogDto keyCorrelationLogDto : dtoList ) {
            list.add( toEntity( keyCorrelationLogDto ) );
        }

        return list;
    }

    @Override
    public List<KeyCorrelationLogDto> toDto(List<KeyCorrelationLog> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KeyCorrelationLogDto> list = new ArrayList<KeyCorrelationLogDto>( entityList.size() );
        for ( KeyCorrelationLog keyCorrelationLog : entityList ) {
            list.add( toDto( keyCorrelationLog ) );
        }

        return list;
    }
}
