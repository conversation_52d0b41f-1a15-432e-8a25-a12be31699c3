package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditLogResult;
import com.wzsec.modules.mask.service.dto.MaskAuditLogResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAuditLogResultMapperImpl implements MaskAuditLogResultMapper {

    @Override
    public MaskAuditLogResult toEntity(MaskAuditLogResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditLogResult maskAuditLogResult = new MaskAuditLogResult();

        maskAuditLogResult.setId( dto.getId() );
        maskAuditLogResult.setTaskname( dto.getTaskname() );
        maskAuditLogResult.setCustname( dto.getCustname() );
        maskAuditLogResult.setCustSimplename( dto.getCustSimplename() );
        maskAuditLogResult.setUserid( dto.getUserid() );
        maskAuditLogResult.setAppid( dto.getAppid() );
        maskAuditLogResult.setAppname( dto.getAppname() );
        maskAuditLogResult.setLogsign( dto.getLogsign() );
        maskAuditLogResult.setApicode( dto.getApicode() );
        maskAuditLogResult.setApiname( dto.getApiname() );
        maskAuditLogResult.setApimethod( dto.getApimethod() );
        maskAuditLogResult.setApitype( dto.getApitype() );
        maskAuditLogResult.setUrl( dto.getUrl() );
        maskAuditLogResult.setRisk( dto.getRisk() );
        maskAuditLogResult.setCheckcount( dto.getCheckcount() );
        maskAuditLogResult.setChecktime( dto.getChecktime() );
        maskAuditLogResult.setSparefield1( dto.getSparefield1() );
        maskAuditLogResult.setSparefield2( dto.getSparefield2() );
        maskAuditLogResult.setSparefield3( dto.getSparefield3() );
        maskAuditLogResult.setSparefield4( dto.getSparefield4() );

        return maskAuditLogResult;
    }

    @Override
    public MaskAuditLogResultDto toDto(MaskAuditLogResult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditLogResultDto maskAuditLogResultDto = new MaskAuditLogResultDto();

        maskAuditLogResultDto.setId( entity.getId() );
        maskAuditLogResultDto.setTaskname( entity.getTaskname() );
        maskAuditLogResultDto.setCustname( entity.getCustname() );
        maskAuditLogResultDto.setCustSimplename( entity.getCustSimplename() );
        maskAuditLogResultDto.setUserid( entity.getUserid() );
        maskAuditLogResultDto.setAppid( entity.getAppid() );
        maskAuditLogResultDto.setAppname( entity.getAppname() );
        maskAuditLogResultDto.setLogsign( entity.getLogsign() );
        maskAuditLogResultDto.setApicode( entity.getApicode() );
        maskAuditLogResultDto.setApiname( entity.getApiname() );
        maskAuditLogResultDto.setApimethod( entity.getApimethod() );
        maskAuditLogResultDto.setApitype( entity.getApitype() );
        maskAuditLogResultDto.setUrl( entity.getUrl() );
        maskAuditLogResultDto.setRisk( entity.getRisk() );
        maskAuditLogResultDto.setCheckcount( entity.getCheckcount() );
        maskAuditLogResultDto.setChecktime( entity.getChecktime() );
        maskAuditLogResultDto.setSparefield1( entity.getSparefield1() );
        maskAuditLogResultDto.setSparefield2( entity.getSparefield2() );
        maskAuditLogResultDto.setSparefield3( entity.getSparefield3() );
        maskAuditLogResultDto.setSparefield4( entity.getSparefield4() );

        return maskAuditLogResultDto;
    }

    @Override
    public List<MaskAuditLogResult> toEntity(List<MaskAuditLogResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditLogResult> list = new ArrayList<MaskAuditLogResult>( dtoList.size() );
        for ( MaskAuditLogResultDto maskAuditLogResultDto : dtoList ) {
            list.add( toEntity( maskAuditLogResultDto ) );
        }

        return list;
    }

    @Override
    public List<MaskAuditLogResultDto> toDto(List<MaskAuditLogResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditLogResultDto> list = new ArrayList<MaskAuditLogResultDto>( entityList.size() );
        for ( MaskAuditLogResult maskAuditLogResult : entityList ) {
            list.add( toDto( maskAuditLogResult ) );
        }

        return list;
    }
}
