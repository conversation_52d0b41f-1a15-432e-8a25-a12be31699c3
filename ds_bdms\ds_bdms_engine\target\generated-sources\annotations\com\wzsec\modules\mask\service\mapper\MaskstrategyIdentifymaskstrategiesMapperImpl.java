package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskstrategyIdentifymaskstrategies;
import com.wzsec.modules.mask.service.dto.MaskstrategyIdentifymaskstrategiesDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskstrategyIdentifymaskstrategiesMapperImpl implements MaskstrategyIdentifymaskstrategiesMapper {

    @Override
    public MaskstrategyIdentifymaskstrategies toEntity(MaskstrategyIdentifymaskstrategiesDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategies maskstrategyIdentifymaskstrategies = new MaskstrategyIdentifymaskstrategies();

        maskstrategyIdentifymaskstrategies.setId( dto.getId() );
        maskstrategyIdentifymaskstrategies.setStrategyname( dto.getStrategyname() );
        maskstrategyIdentifymaskstrategies.setStrategydesc( dto.getStrategydesc() );
        maskstrategyIdentifymaskstrategies.setStatus( dto.getStatus() );
        maskstrategyIdentifymaskstrategies.setCreateuser( dto.getCreateuser() );
        maskstrategyIdentifymaskstrategies.setCreatetime( dto.getCreatetime() );
        maskstrategyIdentifymaskstrategies.setUpdateuser( dto.getUpdateuser() );
        maskstrategyIdentifymaskstrategies.setUpdatetime( dto.getUpdatetime() );
        maskstrategyIdentifymaskstrategies.setRemark( dto.getRemark() );
        maskstrategyIdentifymaskstrategies.setSparefield1( dto.getSparefield1() );
        maskstrategyIdentifymaskstrategies.setSparefield2( dto.getSparefield2() );
        maskstrategyIdentifymaskstrategies.setSparefield3( dto.getSparefield3() );
        maskstrategyIdentifymaskstrategies.setSparefield4( dto.getSparefield4() );
        maskstrategyIdentifymaskstrategies.setEnabled( dto.getEnabled() );

        return maskstrategyIdentifymaskstrategies;
    }

    @Override
    public MaskstrategyIdentifymaskstrategiesDto toDto(MaskstrategyIdentifymaskstrategies entity) {
        if ( entity == null ) {
            return null;
        }

        MaskstrategyIdentifymaskstrategiesDto maskstrategyIdentifymaskstrategiesDto = new MaskstrategyIdentifymaskstrategiesDto();

        maskstrategyIdentifymaskstrategiesDto.setId( entity.getId() );
        maskstrategyIdentifymaskstrategiesDto.setStrategyname( entity.getStrategyname() );
        maskstrategyIdentifymaskstrategiesDto.setStrategydesc( entity.getStrategydesc() );
        maskstrategyIdentifymaskstrategiesDto.setStatus( entity.getStatus() );
        maskstrategyIdentifymaskstrategiesDto.setCreateuser( entity.getCreateuser() );
        maskstrategyIdentifymaskstrategiesDto.setCreatetime( entity.getCreatetime() );
        maskstrategyIdentifymaskstrategiesDto.setUpdateuser( entity.getUpdateuser() );
        maskstrategyIdentifymaskstrategiesDto.setUpdatetime( entity.getUpdatetime() );
        maskstrategyIdentifymaskstrategiesDto.setRemark( entity.getRemark() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield1( entity.getSparefield1() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield2( entity.getSparefield2() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield3( entity.getSparefield3() );
        maskstrategyIdentifymaskstrategiesDto.setSparefield4( entity.getSparefield4() );
        maskstrategyIdentifymaskstrategiesDto.setEnabled( entity.getEnabled() );

        return maskstrategyIdentifymaskstrategiesDto;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategies> toEntity(List<MaskstrategyIdentifymaskstrategiesDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategies> list = new ArrayList<MaskstrategyIdentifymaskstrategies>( dtoList.size() );
        for ( MaskstrategyIdentifymaskstrategiesDto maskstrategyIdentifymaskstrategiesDto : dtoList ) {
            list.add( toEntity( maskstrategyIdentifymaskstrategiesDto ) );
        }

        return list;
    }

    @Override
    public List<MaskstrategyIdentifymaskstrategiesDto> toDto(List<MaskstrategyIdentifymaskstrategies> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskstrategyIdentifymaskstrategiesDto> list = new ArrayList<MaskstrategyIdentifymaskstrategiesDto>( entityList.size() );
        for ( MaskstrategyIdentifymaskstrategies maskstrategyIdentifymaskstrategies : entityList ) {
            list.add( toDto( maskstrategyIdentifymaskstrategies ) );
        }

        return list;
    }
}
