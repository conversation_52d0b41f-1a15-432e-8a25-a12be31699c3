package com.wzsec.modules.sdd.source.service.mapper;

import com.wzsec.modules.sdd.source.domain.Datasource;
import com.wzsec.modules.sdd.source.service.dto.DatasourceSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DatasourceSmallMapperImpl implements DatasourceSmallMapper {

    @Override
    public Datasource toEntity(DatasourceSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datasource datasource = new Datasource();

        datasource.setId( dto.getId() );
        datasource.setSrcname( dto.getSrcname() );
        datasource.setType( dto.getType() );
        datasource.setDriverprogram( dto.getDriverprogram() );
        datasource.setSrcurl( dto.getSrcurl() );
        datasource.setDbname( dto.getDbname() );

        return datasource;
    }

    @Override
    public DatasourceSmallDto toDto(Datasource entity) {
        if ( entity == null ) {
            return null;
        }

        DatasourceSmallDto datasourceSmallDto = new DatasourceSmallDto();

        datasourceSmallDto.setId( entity.getId() );
        datasourceSmallDto.setSrcname( entity.getSrcname() );
        datasourceSmallDto.setType( entity.getType() );
        datasourceSmallDto.setDriverprogram( entity.getDriverprogram() );
        datasourceSmallDto.setSrcurl( entity.getSrcurl() );
        datasourceSmallDto.setDbname( entity.getDbname() );

        return datasourceSmallDto;
    }

    @Override
    public List<Datasource> toEntity(List<DatasourceSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datasource> list = new ArrayList<Datasource>( dtoList.size() );
        for ( DatasourceSmallDto datasourceSmallDto : dtoList ) {
            list.add( toEntity( datasourceSmallDto ) );
        }

        return list;
    }

    @Override
    public List<DatasourceSmallDto> toDto(List<Datasource> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatasourceSmallDto> list = new ArrayList<DatasourceSmallDto>( entityList.size() );
        for ( Datasource datasource : entityList ) {
            list.add( toDto( datasource ) );
        }

        return list;
    }
}
