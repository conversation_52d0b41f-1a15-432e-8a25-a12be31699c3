package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskTablestructure;
import com.wzsec.modules.mask.service.dto.MaskTablestructureDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskTablestructureMapperImpl implements MaskTablestructureMapper {

    @Override
    public MaskTablestructure toEntity(MaskTablestructureDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskTablestructure maskTablestructure = new MaskTablestructure();

        maskTablestructure.setId( dto.getId() );
        maskTablestructure.setFieldid( dto.getFieldid() );
        maskTablestructure.setFieldename( dto.getFieldename() );
        maskTablestructure.setFieldcname( dto.getFieldcname() );
        maskTablestructure.setTableid( dto.getTableid() );
        maskTablestructure.setTablename( dto.getTablename() );
        maskTablestructure.setTablecname( dto.getTablecname() );
        maskTablestructure.setDbname( dto.getDbname() );
        maskTablestructure.setAttribute( dto.getAttribute() );
        maskTablestructure.setSource( dto.getSource() );
        maskTablestructure.setCreatetime( dto.getCreatetime() );
        maskTablestructure.setUpdatetime( dto.getUpdatetime() );
        maskTablestructure.setSparefield1( dto.getSparefield1() );
        maskTablestructure.setSparefield2( dto.getSparefield2() );
        maskTablestructure.setSparefield3( dto.getSparefield3() );
        maskTablestructure.setSparefield4( dto.getSparefield4() );

        return maskTablestructure;
    }

    @Override
    public MaskTablestructureDto toDto(MaskTablestructure entity) {
        if ( entity == null ) {
            return null;
        }

        MaskTablestructureDto maskTablestructureDto = new MaskTablestructureDto();

        maskTablestructureDto.setId( entity.getId() );
        maskTablestructureDto.setFieldid( entity.getFieldid() );
        maskTablestructureDto.setFieldename( entity.getFieldename() );
        maskTablestructureDto.setFieldcname( entity.getFieldcname() );
        maskTablestructureDto.setTableid( entity.getTableid() );
        maskTablestructureDto.setTablename( entity.getTablename() );
        maskTablestructureDto.setTablecname( entity.getTablecname() );
        maskTablestructureDto.setDbname( entity.getDbname() );
        maskTablestructureDto.setAttribute( entity.getAttribute() );
        maskTablestructureDto.setSource( entity.getSource() );
        maskTablestructureDto.setCreatetime( entity.getCreatetime() );
        maskTablestructureDto.setUpdatetime( entity.getUpdatetime() );
        maskTablestructureDto.setSparefield1( entity.getSparefield1() );
        maskTablestructureDto.setSparefield2( entity.getSparefield2() );
        maskTablestructureDto.setSparefield3( entity.getSparefield3() );
        maskTablestructureDto.setSparefield4( entity.getSparefield4() );

        return maskTablestructureDto;
    }

    @Override
    public List<MaskTablestructure> toEntity(List<MaskTablestructureDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskTablestructure> list = new ArrayList<MaskTablestructure>( dtoList.size() );
        for ( MaskTablestructureDto maskTablestructureDto : dtoList ) {
            list.add( toEntity( maskTablestructureDto ) );
        }

        return list;
    }

    @Override
    public List<MaskTablestructureDto> toDto(List<MaskTablestructure> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskTablestructureDto> list = new ArrayList<MaskTablestructureDto>( entityList.size() );
        for ( MaskTablestructure maskTablestructure : entityList ) {
            list.add( toDto( maskTablestructure ) );
        }

        return list;
    }
}
