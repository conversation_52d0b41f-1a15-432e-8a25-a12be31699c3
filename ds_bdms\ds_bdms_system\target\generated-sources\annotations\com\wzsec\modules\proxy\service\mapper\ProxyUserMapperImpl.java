package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.ProxyUser;
import com.wzsec.modules.proxy.service.dto.ProxyUserDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ProxyUserMapperImpl implements ProxyUserMapper {

    @Override
    public ProxyUser toEntity(ProxyUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        ProxyUser proxyUser = new ProxyUser();

        proxyUser.setId( dto.getId() );
        proxyUser.setUsername( dto.getUsername() );
        proxyUser.setPassword( dto.getPassword() );
        proxyUser.setCname( dto.getCname() );
        proxyUser.setRole( dto.getRole() );
        proxyUser.setSrcid( dto.getSrcid() );
        proxyUser.setStarttime( dto.getStarttime() );
        proxyUser.setEndtime( dto.getEndtime() );
        proxyUser.setDbname( dto.getDbname() );
        proxyUser.setState( dto.getState() );
        proxyUser.setCreateuser( dto.getCreateuser() );
        proxyUser.setCreatetime( dto.getCreatetime() );
        proxyUser.setUpdateuser( dto.getUpdateuser() );
        proxyUser.setUpdatetime( dto.getUpdatetime() );
        proxyUser.setMemo( dto.getMemo() );
        proxyUser.setSparefield1( dto.getSparefield1() );
        proxyUser.setSparefield2( dto.getSparefield2() );
        proxyUser.setSparefield3( dto.getSparefield3() );
        proxyUser.setSparefield4( dto.getSparefield4() );
        proxyUser.setSparefield5( dto.getSparefield5() );

        return proxyUser;
    }

    @Override
    public ProxyUserDto toDto(ProxyUser entity) {
        if ( entity == null ) {
            return null;
        }

        ProxyUserDto proxyUserDto = new ProxyUserDto();

        proxyUserDto.setId( entity.getId() );
        proxyUserDto.setUsername( entity.getUsername() );
        proxyUserDto.setPassword( entity.getPassword() );
        proxyUserDto.setCname( entity.getCname() );
        proxyUserDto.setRole( entity.getRole() );
        proxyUserDto.setSrcid( entity.getSrcid() );
        proxyUserDto.setStarttime( entity.getStarttime() );
        proxyUserDto.setEndtime( entity.getEndtime() );
        proxyUserDto.setDbname( entity.getDbname() );
        proxyUserDto.setState( entity.getState() );
        proxyUserDto.setCreateuser( entity.getCreateuser() );
        proxyUserDto.setCreatetime( entity.getCreatetime() );
        proxyUserDto.setUpdateuser( entity.getUpdateuser() );
        proxyUserDto.setUpdatetime( entity.getUpdatetime() );
        proxyUserDto.setMemo( entity.getMemo() );
        proxyUserDto.setSparefield1( entity.getSparefield1() );
        proxyUserDto.setSparefield2( entity.getSparefield2() );
        proxyUserDto.setSparefield3( entity.getSparefield3() );
        proxyUserDto.setSparefield4( entity.getSparefield4() );
        proxyUserDto.setSparefield5( entity.getSparefield5() );

        return proxyUserDto;
    }

    @Override
    public List<ProxyUser> toEntity(List<ProxyUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ProxyUser> list = new ArrayList<ProxyUser>( dtoList.size() );
        for ( ProxyUserDto proxyUserDto : dtoList ) {
            list.add( toEntity( proxyUserDto ) );
        }

        return list;
    }

    @Override
    public List<ProxyUserDto> toDto(List<ProxyUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ProxyUserDto> list = new ArrayList<ProxyUserDto>( entityList.size() );
        for ( ProxyUser proxyUser : entityList ) {
            list.add( toDto( proxyUser ) );
        }

        return list;
    }
}
