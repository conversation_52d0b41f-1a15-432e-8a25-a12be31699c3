package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.AlarmSettings;
import com.wzsec.modules.mask.service.dto.AlarmSettingsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class AlarmSettingsMapperImpl implements AlarmSettingsMapper {

    @Override
    public AlarmSettings toEntity(AlarmSettingsDto dto) {
        if ( dto == null ) {
            return null;
        }

        AlarmSettings alarmSettings = new AlarmSettings();

        alarmSettings.setId( dto.getId() );
        alarmSettings.setFaultname( dto.getFaultname() );
        alarmSettings.setMessage( dto.getMessage() );
        alarmSettings.setAlarmlevel( dto.getAlarmlevel() );
        alarmSettings.setThreshold( dto.getThreshold() );
        alarmSettings.setState( dto.getState() );
        alarmSettings.setCreateuser( dto.getCreateuser() );
        alarmSettings.setCreatetime( dto.getCreatetime() );
        alarmSettings.setUpdateuser( dto.getUpdateuser() );
        alarmSettings.setUpdatetime( dto.getUpdatetime() );
        alarmSettings.setNote( dto.getNote() );
        alarmSettings.setSparefield1( dto.getSparefield1() );
        alarmSettings.setSparefield2( dto.getSparefield2() );
        alarmSettings.setSparefield3( dto.getSparefield3() );
        alarmSettings.setSparefield4( dto.getSparefield4() );
        alarmSettings.setSparefield5( dto.getSparefield5() );

        return alarmSettings;
    }

    @Override
    public AlarmSettingsDto toDto(AlarmSettings entity) {
        if ( entity == null ) {
            return null;
        }

        AlarmSettingsDto alarmSettingsDto = new AlarmSettingsDto();

        alarmSettingsDto.setId( entity.getId() );
        alarmSettingsDto.setFaultname( entity.getFaultname() );
        alarmSettingsDto.setMessage( entity.getMessage() );
        alarmSettingsDto.setAlarmlevel( entity.getAlarmlevel() );
        alarmSettingsDto.setThreshold( entity.getThreshold() );
        alarmSettingsDto.setState( entity.getState() );
        alarmSettingsDto.setCreateuser( entity.getCreateuser() );
        alarmSettingsDto.setCreatetime( entity.getCreatetime() );
        alarmSettingsDto.setUpdateuser( entity.getUpdateuser() );
        alarmSettingsDto.setUpdatetime( entity.getUpdatetime() );
        alarmSettingsDto.setNote( entity.getNote() );
        alarmSettingsDto.setSparefield1( entity.getSparefield1() );
        alarmSettingsDto.setSparefield2( entity.getSparefield2() );
        alarmSettingsDto.setSparefield3( entity.getSparefield3() );
        alarmSettingsDto.setSparefield4( entity.getSparefield4() );
        alarmSettingsDto.setSparefield5( entity.getSparefield5() );

        return alarmSettingsDto;
    }

    @Override
    public List<AlarmSettings> toEntity(List<AlarmSettingsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<AlarmSettings> list = new ArrayList<AlarmSettings>( dtoList.size() );
        for ( AlarmSettingsDto alarmSettingsDto : dtoList ) {
            list.add( toEntity( alarmSettingsDto ) );
        }

        return list;
    }

    @Override
    public List<AlarmSettingsDto> toDto(List<AlarmSettings> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AlarmSettingsDto> list = new ArrayList<AlarmSettingsDto>( entityList.size() );
        for ( AlarmSettings alarmSettings : entityList ) {
            list.add( toDto( alarmSettings ) );
        }

        return list;
    }
}
