package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HiveTaskResult;
import com.wzsec.modules.mask.service.dto.HiveTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class HiveTaskResultMapperImpl implements HiveTaskResultMapper {

    @Override
    public HiveTaskResult toEntity(HiveTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        HiveTaskResult hiveTaskResult = new HiveTaskResult();

        hiveTaskResult.setId( dto.getId() );
        hiveTaskResult.setTaskname( dto.getTaskname() );
        hiveTaskResult.setPlatform( dto.getPlatform() );
        hiveTaskResult.setDbname( dto.getDbname() );
        hiveTaskResult.setTablename( dto.getTablename() );
        hiveTaskResult.setPartitioninfo( dto.getPartitioninfo() );
        hiveTaskResult.setQueuename( dto.getQueuename() );
        hiveTaskResult.setMaskstrategystr( dto.getMaskstrategystr() );
        hiveTaskResult.setFileformat( dto.getFileformat() );
        hiveTaskResult.setDatainputpath( dto.getDatainputpath() );
        hiveTaskResult.setDataoutputpath( dto.getDataoutputpath() );
        hiveTaskResult.setDatafileinputpath( dto.getDatafileinputpath() );
        hiveTaskResult.setDatafileoutputpath( dto.getDatafileoutputpath() );
        hiveTaskResult.setDatasplit( dto.getDatasplit() );
        hiveTaskResult.setJobstarttime( dto.getJobstarttime() );
        hiveTaskResult.setJobendtime( dto.getJobendtime() );
        hiveTaskResult.setJobtotaltime( dto.getJobtotaltime() );
        hiveTaskResult.setJobstatus( dto.getJobstatus() );
        hiveTaskResult.setDatarows( dto.getDatarows() );
        hiveTaskResult.setCreatetime( dto.getCreatetime() );
        hiveTaskResult.setUpdatetime( dto.getUpdatetime() );
        hiveTaskResult.setUserid( dto.getUserid() );
        hiveTaskResult.setUsername( dto.getUsername() );
        hiveTaskResult.setRemark( dto.getRemark() );
        hiveTaskResult.setSparefield1( dto.getSparefield1() );
        hiveTaskResult.setSparefield2( dto.getSparefield2() );
        hiveTaskResult.setSparefield3( dto.getSparefield3() );
        hiveTaskResult.setSparefield4( dto.getSparefield4() );
        hiveTaskResult.setSparefield5( dto.getSparefield5() );

        return hiveTaskResult;
    }

    @Override
    public HiveTaskResultDto toDto(HiveTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        HiveTaskResultDto hiveTaskResultDto = new HiveTaskResultDto();

        hiveTaskResultDto.setId( entity.getId() );
        hiveTaskResultDto.setTaskname( entity.getTaskname() );
        hiveTaskResultDto.setPlatform( entity.getPlatform() );
        hiveTaskResultDto.setDbname( entity.getDbname() );
        hiveTaskResultDto.setTablename( entity.getTablename() );
        hiveTaskResultDto.setPartitioninfo( entity.getPartitioninfo() );
        hiveTaskResultDto.setQueuename( entity.getQueuename() );
        hiveTaskResultDto.setMaskstrategystr( entity.getMaskstrategystr() );
        hiveTaskResultDto.setFileformat( entity.getFileformat() );
        hiveTaskResultDto.setDatainputpath( entity.getDatainputpath() );
        hiveTaskResultDto.setDataoutputpath( entity.getDataoutputpath() );
        hiveTaskResultDto.setDatafileinputpath( entity.getDatafileinputpath() );
        hiveTaskResultDto.setDatafileoutputpath( entity.getDatafileoutputpath() );
        hiveTaskResultDto.setDatasplit( entity.getDatasplit() );
        hiveTaskResultDto.setJobstarttime( entity.getJobstarttime() );
        hiveTaskResultDto.setJobendtime( entity.getJobendtime() );
        hiveTaskResultDto.setJobtotaltime( entity.getJobtotaltime() );
        hiveTaskResultDto.setJobstatus( entity.getJobstatus() );
        hiveTaskResultDto.setDatarows( entity.getDatarows() );
        hiveTaskResultDto.setCreatetime( entity.getCreatetime() );
        hiveTaskResultDto.setUpdatetime( entity.getUpdatetime() );
        hiveTaskResultDto.setUserid( entity.getUserid() );
        hiveTaskResultDto.setUsername( entity.getUsername() );
        hiveTaskResultDto.setRemark( entity.getRemark() );
        hiveTaskResultDto.setSparefield1( entity.getSparefield1() );
        hiveTaskResultDto.setSparefield2( entity.getSparefield2() );
        hiveTaskResultDto.setSparefield3( entity.getSparefield3() );
        hiveTaskResultDto.setSparefield4( entity.getSparefield4() );
        hiveTaskResultDto.setSparefield5( entity.getSparefield5() );

        return hiveTaskResultDto;
    }

    @Override
    public List<HiveTaskResult> toEntity(List<HiveTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HiveTaskResult> list = new ArrayList<HiveTaskResult>( dtoList.size() );
        for ( HiveTaskResultDto hiveTaskResultDto : dtoList ) {
            list.add( toEntity( hiveTaskResultDto ) );
        }

        return list;
    }

    @Override
    public List<HiveTaskResultDto> toDto(List<HiveTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HiveTaskResultDto> list = new ArrayList<HiveTaskResultDto>( entityList.size() );
        for ( HiveTaskResult hiveTaskResult : entityList ) {
            list.add( toDto( hiveTaskResult ) );
        }

        return list;
    }
}
