package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.HiveTaskConfig;
import com.wzsec.modules.mask.service.dto.HiveTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class HiveTaskConfigMapperImpl implements HiveTaskConfigMapper {

    @Override
    public HiveTaskConfig toEntity(HiveTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        HiveTaskConfig hiveTaskConfig = new HiveTaskConfig();

        hiveTaskConfig.setId( dto.getId() );
        hiveTaskConfig.setTaskname( dto.getTaskname() );
        hiveTaskConfig.setSourceid( dto.getSourceid() );
        hiveTaskConfig.setDbname( dto.getDbname() );
        hiveTaskConfig.setTablename( dto.getTablename() );
        hiveTaskConfig.setMaskedtable( dto.getMaskedtable() );
        hiveTaskConfig.setStrategyid( dto.getStrategyid() );
        hiveTaskConfig.setJarpath( dto.getJarpath() );
        hiveTaskConfig.setQueuename( dto.getQueuename() );
        hiveTaskConfig.setPartitioninfo( dto.getPartitioninfo() );
        hiveTaskConfig.setDatarows( dto.getDatarows() );
        hiveTaskConfig.setOutputtype( dto.getOutputtype() );
        hiveTaskConfig.setOutputdbname( dto.getOutputdbname() );
        hiveTaskConfig.setOutputdir( dto.getOutputdir() );
        hiveTaskConfig.setDatasplit( dto.getDatasplit() );
        hiveTaskConfig.setFileformat( dto.getFileformat() );
        hiveTaskConfig.setStatus( dto.getStatus() );
        hiveTaskConfig.setFieldnames( dto.getFieldnames() );
        hiveTaskConfig.setExtractfields( dto.getExtractfields() );
        hiveTaskConfig.setMaskfieldnames( dto.getMaskfieldnames() );
        hiveTaskConfig.setMaskstrategystr( dto.getMaskstrategystr() );
        hiveTaskConfig.setCreateuser( dto.getCreateuser() );
        hiveTaskConfig.setCreatetime( dto.getCreatetime() );
        hiveTaskConfig.setUpdateuser( dto.getUpdateuser() );
        hiveTaskConfig.setUpdatetime( dto.getUpdatetime() );
        hiveTaskConfig.setRemark( dto.getRemark() );
        hiveTaskConfig.setSparefield1( dto.getSparefield1() );
        hiveTaskConfig.setSparefield2( dto.getSparefield2() );
        hiveTaskConfig.setSparefield3( dto.getSparefield3() );
        hiveTaskConfig.setSparefield4( dto.getSparefield4() );
        hiveTaskConfig.setSparefield5( dto.getSparefield5() );

        return hiveTaskConfig;
    }

    @Override
    public HiveTaskConfigDto toDto(HiveTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        HiveTaskConfigDto hiveTaskConfigDto = new HiveTaskConfigDto();

        hiveTaskConfigDto.setId( entity.getId() );
        hiveTaskConfigDto.setTaskname( entity.getTaskname() );
        hiveTaskConfigDto.setSourceid( entity.getSourceid() );
        hiveTaskConfigDto.setDbname( entity.getDbname() );
        hiveTaskConfigDto.setTablename( entity.getTablename() );
        hiveTaskConfigDto.setMaskedtable( entity.getMaskedtable() );
        hiveTaskConfigDto.setStrategyid( entity.getStrategyid() );
        hiveTaskConfigDto.setJarpath( entity.getJarpath() );
        hiveTaskConfigDto.setQueuename( entity.getQueuename() );
        hiveTaskConfigDto.setPartitioninfo( entity.getPartitioninfo() );
        hiveTaskConfigDto.setDatarows( entity.getDatarows() );
        hiveTaskConfigDto.setOutputtype( entity.getOutputtype() );
        hiveTaskConfigDto.setOutputdbname( entity.getOutputdbname() );
        hiveTaskConfigDto.setOutputdir( entity.getOutputdir() );
        hiveTaskConfigDto.setDatasplit( entity.getDatasplit() );
        hiveTaskConfigDto.setFileformat( entity.getFileformat() );
        hiveTaskConfigDto.setStatus( entity.getStatus() );
        hiveTaskConfigDto.setFieldnames( entity.getFieldnames() );
        hiveTaskConfigDto.setExtractfields( entity.getExtractfields() );
        hiveTaskConfigDto.setMaskfieldnames( entity.getMaskfieldnames() );
        hiveTaskConfigDto.setMaskstrategystr( entity.getMaskstrategystr() );
        hiveTaskConfigDto.setCreateuser( entity.getCreateuser() );
        hiveTaskConfigDto.setCreatetime( entity.getCreatetime() );
        hiveTaskConfigDto.setUpdateuser( entity.getUpdateuser() );
        hiveTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        hiveTaskConfigDto.setRemark( entity.getRemark() );
        hiveTaskConfigDto.setSparefield1( entity.getSparefield1() );
        hiveTaskConfigDto.setSparefield2( entity.getSparefield2() );
        hiveTaskConfigDto.setSparefield3( entity.getSparefield3() );
        hiveTaskConfigDto.setSparefield4( entity.getSparefield4() );
        hiveTaskConfigDto.setSparefield5( entity.getSparefield5() );

        return hiveTaskConfigDto;
    }

    @Override
    public List<HiveTaskConfig> toEntity(List<HiveTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<HiveTaskConfig> list = new ArrayList<HiveTaskConfig>( dtoList.size() );
        for ( HiveTaskConfigDto hiveTaskConfigDto : dtoList ) {
            list.add( toEntity( hiveTaskConfigDto ) );
        }

        return list;
    }

    @Override
    public List<HiveTaskConfigDto> toDto(List<HiveTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HiveTaskConfigDto> list = new ArrayList<HiveTaskConfigDto>( entityList.size() );
        for ( HiveTaskConfig hiveTaskConfig : entityList ) {
            list.add( toDto( hiveTaskConfig ) );
        }

        return list;
    }
}
