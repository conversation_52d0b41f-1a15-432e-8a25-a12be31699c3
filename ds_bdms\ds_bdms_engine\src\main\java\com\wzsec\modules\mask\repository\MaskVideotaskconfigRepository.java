package com.wzsec.modules.mask.repository;

import com.wzsec.modules.mask.domain.MaskVideotaskconfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2022-04-21
 */
public interface MaskVideotaskconfigRepository extends JpaRepository<MaskVideotaskconfig, Integer>, JpaSpecificationExecutor<MaskVideotaskconfig> {

    /**
     * 根据ID修改状态
     *
     * @param id
     */
    @Transactional
    @Modifying
    @Query(value = "UPDATE sdd_mask_videotaskconfig SET executionstate =?2 WHERE id=?1 ", nativeQuery = true)
    void updateTaskStatus(Integer id, String status);
}