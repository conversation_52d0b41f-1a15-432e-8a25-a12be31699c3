package com.wzsec.modules.mask.service;

import com.wzsec.modules.mask.domain.MaskVideotaskconfig;
import com.wzsec.modules.mask.service.dto.MaskVideotaskconfigDto;
import com.wzsec.modules.mask.service.dto.MaskVideotaskconfigQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-04-21
 */
public interface MaskVideotaskconfigService {

    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(MaskVideotaskconfigQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     * @param criteria 条件参数
     * @return List<MaskVideotaskconfigDto>
     */
    List<MaskVideotaskconfigDto> queryAll(MaskVideotaskconfigQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return MaskVideotaskconfigDto
     */
    MaskVideotaskconfigDto findById(Integer id);

    /**
     * 根据ID查询返回实体对象
     * @param id ID
     * @return findMaskVideoTaskConfigById
     */
    MaskVideotaskconfig findMaskVideoTaskConfigById(Integer id);

    /**
     * 创建
     * @param resources /
     * @return MaskVideotaskconfigDto
     */
    MaskVideotaskconfigDto create(MaskVideotaskconfig resources);

    /**
     * 编辑
     * @param resources /
     */
    void update(MaskVideotaskconfig resources);

    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     * @param all 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<MaskVideotaskconfigDto> all, HttpServletResponse response) throws IOException;


    /**
     * 根据ID更新任务状态
     *
     * @param id id
     */
    void updateTaskStatus(Integer id, String status);
}