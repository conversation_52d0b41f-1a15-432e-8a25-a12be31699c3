package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditTaskV1;
import com.wzsec.modules.mask.service.dto.MaskAuditTaskV1Dto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAuditTaskV1MapperImpl implements MaskAuditTaskV1Mapper {

    @Override
    public MaskAuditTaskV1 toEntity(MaskAuditTaskV1Dto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditTaskV1 maskAuditTaskV1 = new MaskAuditTaskV1();

        maskAuditTaskV1.setId( dto.getId() );
        maskAuditTaskV1.setTaskname( dto.getTaskname() );
        maskAuditTaskV1.setSystemid( dto.getSystemid() );
        maskAuditTaskV1.setSubmitmethod( dto.getSubmitmethod() );
        maskAuditTaskV1.setCron( dto.getCron() );
        maskAuditTaskV1.setChecktype( dto.getChecktype() );
        maskAuditTaskV1.setDisstrategy( dto.getDisstrategy() );
        maskAuditTaskV1.setMaskstrategy( dto.getMaskstrategy() );
        maskAuditTaskV1.setOutsourceid( dto.getOutsourceid() );
        maskAuditTaskV1.setTablename( dto.getTablename() );
        maskAuditTaskV1.setOutfilepath( dto.getOutfilepath() );
        maskAuditTaskV1.setFiletype( dto.getFiletype() );
        maskAuditTaskV1.setFilespilt( dto.getFilespilt() );
        maskAuditTaskV1.setStatus( dto.getStatus() );
        maskAuditTaskV1.setExecutionstate( dto.getExecutionstate() );
        maskAuditTaskV1.setCreateuser( dto.getCreateuser() );
        maskAuditTaskV1.setCreatetime( dto.getCreatetime() );
        maskAuditTaskV1.setUpdateuser( dto.getUpdateuser() );
        maskAuditTaskV1.setUpdatetime( dto.getUpdatetime() );
        maskAuditTaskV1.setRemark( dto.getRemark() );
        maskAuditTaskV1.setMasktask( dto.getMasktask() );
        maskAuditTaskV1.setSparefield1( dto.getSparefield1() );
        maskAuditTaskV1.setSparefield2( dto.getSparefield2() );
        maskAuditTaskV1.setSparefield3( dto.getSparefield3() );
        maskAuditTaskV1.setSparefield4( dto.getSparefield4() );

        return maskAuditTaskV1;
    }

    @Override
    public MaskAuditTaskV1Dto toDto(MaskAuditTaskV1 entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditTaskV1Dto maskAuditTaskV1Dto = new MaskAuditTaskV1Dto();

        maskAuditTaskV1Dto.setId( entity.getId() );
        maskAuditTaskV1Dto.setTaskname( entity.getTaskname() );
        maskAuditTaskV1Dto.setSystemid( entity.getSystemid() );
        maskAuditTaskV1Dto.setSubmitmethod( entity.getSubmitmethod() );
        maskAuditTaskV1Dto.setCron( entity.getCron() );
        maskAuditTaskV1Dto.setChecktype( entity.getChecktype() );
        maskAuditTaskV1Dto.setDisstrategy( entity.getDisstrategy() );
        maskAuditTaskV1Dto.setMaskstrategy( entity.getMaskstrategy() );
        maskAuditTaskV1Dto.setOutsourceid( entity.getOutsourceid() );
        maskAuditTaskV1Dto.setTablename( entity.getTablename() );
        maskAuditTaskV1Dto.setOutfilepath( entity.getOutfilepath() );
        maskAuditTaskV1Dto.setFiletype( entity.getFiletype() );
        maskAuditTaskV1Dto.setFilespilt( entity.getFilespilt() );
        maskAuditTaskV1Dto.setStatus( entity.getStatus() );
        maskAuditTaskV1Dto.setExecutionstate( entity.getExecutionstate() );
        maskAuditTaskV1Dto.setCreateuser( entity.getCreateuser() );
        maskAuditTaskV1Dto.setCreatetime( entity.getCreatetime() );
        maskAuditTaskV1Dto.setUpdateuser( entity.getUpdateuser() );
        maskAuditTaskV1Dto.setUpdatetime( entity.getUpdatetime() );
        maskAuditTaskV1Dto.setRemark( entity.getRemark() );
        maskAuditTaskV1Dto.setMasktask( entity.getMasktask() );
        maskAuditTaskV1Dto.setSparefield1( entity.getSparefield1() );
        maskAuditTaskV1Dto.setSparefield2( entity.getSparefield2() );
        maskAuditTaskV1Dto.setSparefield3( entity.getSparefield3() );
        maskAuditTaskV1Dto.setSparefield4( entity.getSparefield4() );

        return maskAuditTaskV1Dto;
    }

    @Override
    public List<MaskAuditTaskV1> toEntity(List<MaskAuditTaskV1Dto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditTaskV1> list = new ArrayList<MaskAuditTaskV1>( dtoList.size() );
        for ( MaskAuditTaskV1Dto maskAuditTaskV1Dto : dtoList ) {
            list.add( toEntity( maskAuditTaskV1Dto ) );
        }

        return list;
    }

    @Override
    public List<MaskAuditTaskV1Dto> toDto(List<MaskAuditTaskV1> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditTaskV1Dto> list = new ArrayList<MaskAuditTaskV1Dto>( entityList.size() );
        for ( MaskAuditTaskV1 maskAuditTaskV1 : entityList ) {
            list.add( toDto( maskAuditTaskV1 ) );
        }

        return list;
    }
}
