package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyTable;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyTableMapperImpl implements MaskStrategyTableMapper {

    @Override
    public MaskStrategyTable toEntity(MaskStrategyTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyTable maskStrategyTable = new MaskStrategyTable();

        maskStrategyTable.setId( dto.getId() );
        maskStrategyTable.setStrategyname( dto.getStrategyname() );
        maskStrategyTable.setStrategydesc( dto.getStrategydesc() );
        maskStrategyTable.setStrategytype( dto.getStrategytype() );
        maskStrategyTable.setStatus( dto.getStatus() );
        maskStrategyTable.setEnabled( dto.getEnabled() );
        maskStrategyTable.setTabid( dto.getTabid() );
        maskStrategyTable.setTabename( dto.getTabename() );
        maskStrategyTable.setTabcname( dto.getTabcname() );
        maskStrategyTable.setDbname( dto.getDbname() );
        maskStrategyTable.setSourcetype( dto.getSourcetype() );
        maskStrategyTable.setLevelVal( dto.getLevelVal() );
        maskStrategyTable.setCreateuser( dto.getCreateuser() );
        maskStrategyTable.setCreatetime( dto.getCreatetime() );
        maskStrategyTable.setUpdateuser( dto.getUpdateuser() );
        maskStrategyTable.setUpdatetime( dto.getUpdatetime() );
        maskStrategyTable.setMemo( dto.getMemo() );
        maskStrategyTable.setSparefield1( dto.getSparefield1() );
        maskStrategyTable.setSparefield2( dto.getSparefield2() );
        maskStrategyTable.setSparefield3( dto.getSparefield3() );
        maskStrategyTable.setSparefield4( dto.getSparefield4() );
        maskStrategyTable.setSparefield5( dto.getSparefield5() );

        return maskStrategyTable;
    }

    @Override
    public MaskStrategyTableDto toDto(MaskStrategyTable entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyTableDto maskStrategyTableDto = new MaskStrategyTableDto();

        maskStrategyTableDto.setId( entity.getId() );
        maskStrategyTableDto.setStrategyname( entity.getStrategyname() );
        maskStrategyTableDto.setStrategydesc( entity.getStrategydesc() );
        maskStrategyTableDto.setStrategytype( entity.getStrategytype() );
        maskStrategyTableDto.setStatus( entity.getStatus() );
        maskStrategyTableDto.setEnabled( entity.getEnabled() );
        maskStrategyTableDto.setTabid( entity.getTabid() );
        maskStrategyTableDto.setTabename( entity.getTabename() );
        maskStrategyTableDto.setTabcname( entity.getTabcname() );
        maskStrategyTableDto.setDbname( entity.getDbname() );
        maskStrategyTableDto.setSourcetype( entity.getSourcetype() );
        maskStrategyTableDto.setLevelVal( entity.getLevelVal() );
        maskStrategyTableDto.setCreateuser( entity.getCreateuser() );
        maskStrategyTableDto.setCreatetime( entity.getCreatetime() );
        maskStrategyTableDto.setUpdateuser( entity.getUpdateuser() );
        maskStrategyTableDto.setUpdatetime( entity.getUpdatetime() );
        maskStrategyTableDto.setMemo( entity.getMemo() );
        maskStrategyTableDto.setSparefield1( entity.getSparefield1() );
        maskStrategyTableDto.setSparefield2( entity.getSparefield2() );
        maskStrategyTableDto.setSparefield3( entity.getSparefield3() );
        maskStrategyTableDto.setSparefield4( entity.getSparefield4() );
        maskStrategyTableDto.setSparefield5( entity.getSparefield5() );

        return maskStrategyTableDto;
    }

    @Override
    public List<MaskStrategyTable> toEntity(List<MaskStrategyTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyTable> list = new ArrayList<MaskStrategyTable>( dtoList.size() );
        for ( MaskStrategyTableDto maskStrategyTableDto : dtoList ) {
            list.add( toEntity( maskStrategyTableDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyTableDto> toDto(List<MaskStrategyTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyTableDto> list = new ArrayList<MaskStrategyTableDto>( entityList.size() );
        for ( MaskStrategyTable maskStrategyTable : entityList ) {
            list.add( toDto( maskStrategyTable ) );
        }

        return list;
    }
}
