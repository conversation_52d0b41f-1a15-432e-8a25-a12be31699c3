package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditJarLogResult;
import com.wzsec.modules.mask.service.dto.MaskAuditJarLogResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAuditJarLogResultMapperImpl implements MaskAuditJarLogResultMapper {

    @Override
    public MaskAuditJarLogResult toEntity(MaskAuditJarLogResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditJarLogResult maskAuditJarLogResult = new MaskAuditJarLogResult();

        maskAuditJarLogResult.setId( dto.getId() );
        maskAuditJarLogResult.setTaskname( dto.getTaskname() );
        maskAuditJarLogResult.setStrategy( dto.getStrategy() );
        maskAuditJarLogResult.setAlgorithmpackagename( dto.getAlgorithmpackagename() );
        maskAuditJarLogResult.setAlgorithmname( dto.getAlgorithmname() );
        maskAuditJarLogResult.setIpaddress( dto.getIpaddress() );
        maskAuditJarLogResult.setAccount( dto.getAccount() );
        maskAuditJarLogResult.setLinenumber( dto.getLinenumber() );
        maskAuditJarLogResult.setIssuccessful( dto.getIssuccessful() );
        maskAuditJarLogResult.setStarttime( dto.getStarttime() );
        maskAuditJarLogResult.setEndtime( dto.getEndtime() );
        maskAuditJarLogResult.setTotaltime( dto.getTotaltime() );
        maskAuditJarLogResult.setChecktime( dto.getChecktime() );
        maskAuditJarLogResult.setNote( dto.getNote() );
        maskAuditJarLogResult.setSparefield1( dto.getSparefield1() );
        maskAuditJarLogResult.setSparefield2( dto.getSparefield2() );
        maskAuditJarLogResult.setSparefield3( dto.getSparefield3() );
        maskAuditJarLogResult.setSparefield4( dto.getSparefield4() );

        return maskAuditJarLogResult;
    }

    @Override
    public MaskAuditJarLogResultDto toDto(MaskAuditJarLogResult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditJarLogResultDto maskAuditJarLogResultDto = new MaskAuditJarLogResultDto();

        maskAuditJarLogResultDto.setId( entity.getId() );
        maskAuditJarLogResultDto.setTaskname( entity.getTaskname() );
        maskAuditJarLogResultDto.setStrategy( entity.getStrategy() );
        maskAuditJarLogResultDto.setAlgorithmpackagename( entity.getAlgorithmpackagename() );
        maskAuditJarLogResultDto.setAlgorithmname( entity.getAlgorithmname() );
        maskAuditJarLogResultDto.setIpaddress( entity.getIpaddress() );
        maskAuditJarLogResultDto.setAccount( entity.getAccount() );
        maskAuditJarLogResultDto.setLinenumber( entity.getLinenumber() );
        maskAuditJarLogResultDto.setIssuccessful( entity.getIssuccessful() );
        maskAuditJarLogResultDto.setStarttime( entity.getStarttime() );
        maskAuditJarLogResultDto.setEndtime( entity.getEndtime() );
        maskAuditJarLogResultDto.setTotaltime( entity.getTotaltime() );
        maskAuditJarLogResultDto.setChecktime( entity.getChecktime() );
        maskAuditJarLogResultDto.setNote( entity.getNote() );
        maskAuditJarLogResultDto.setSparefield1( entity.getSparefield1() );
        maskAuditJarLogResultDto.setSparefield2( entity.getSparefield2() );
        maskAuditJarLogResultDto.setSparefield3( entity.getSparefield3() );
        maskAuditJarLogResultDto.setSparefield4( entity.getSparefield4() );

        return maskAuditJarLogResultDto;
    }

    @Override
    public List<MaskAuditJarLogResult> toEntity(List<MaskAuditJarLogResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditJarLogResult> list = new ArrayList<MaskAuditJarLogResult>( dtoList.size() );
        for ( MaskAuditJarLogResultDto maskAuditJarLogResultDto : dtoList ) {
            list.add( toEntity( maskAuditJarLogResultDto ) );
        }

        return list;
    }

    @Override
    public List<MaskAuditJarLogResultDto> toDto(List<MaskAuditJarLogResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditJarLogResultDto> list = new ArrayList<MaskAuditJarLogResultDto>( entityList.size() );
        for ( MaskAuditJarLogResult maskAuditJarLogResult : entityList ) {
            list.add( toDto( maskAuditJarLogResult ) );
        }

        return list;
    }
}
