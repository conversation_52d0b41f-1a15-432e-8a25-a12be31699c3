package com.wzsec.modules.proxy.service.mapper;

import com.wzsec.modules.proxy.domain.SQLRule;
import com.wzsec.modules.proxy.service.dto.SQLRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class SQLRuleMapperImpl implements SQLRuleMapper {

    @Override
    public SQLRule toEntity(SQLRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        SQLRule sQLRule = new SQLRule();

        sQLRule.setId( dto.getId() );
        sQLRule.setSname( dto.getSname() );
        sQLRule.setAlgorithm( dto.getAlgorithm() );
        sQLRule.setParam( dto.getParam() );
        sQLRule.setRole( dto.getRole() );
        sQLRule.setState( dto.getState() );
        sQLRule.setCreateuser( dto.getCreateuser() );
        sQLRule.setCreatetime( dto.getCreatetime() );
        sQLRule.setUpdateuser( dto.getUpdateuser() );
        sQLRule.setUpdatetime( dto.getUpdatetime() );
        sQLRule.setNote( dto.getNote() );
        sQLRule.setSparefield1( dto.getSparefield1() );
        sQLRule.setSparefield2( dto.getSparefield2() );
        sQLRule.setSparefield3( dto.getSparefield3() );
        sQLRule.setSparefield4( dto.getSparefield4() );
        sQLRule.setSparefield5( dto.getSparefield5() );

        return sQLRule;
    }

    @Override
    public SQLRuleDto toDto(SQLRule entity) {
        if ( entity == null ) {
            return null;
        }

        SQLRuleDto sQLRuleDto = new SQLRuleDto();

        sQLRuleDto.setId( entity.getId() );
        sQLRuleDto.setSname( entity.getSname() );
        sQLRuleDto.setAlgorithm( entity.getAlgorithm() );
        sQLRuleDto.setParam( entity.getParam() );
        sQLRuleDto.setRole( entity.getRole() );
        sQLRuleDto.setState( entity.getState() );
        sQLRuleDto.setCreateuser( entity.getCreateuser() );
        sQLRuleDto.setCreatetime( entity.getCreatetime() );
        sQLRuleDto.setUpdateuser( entity.getUpdateuser() );
        sQLRuleDto.setUpdatetime( entity.getUpdatetime() );
        sQLRuleDto.setNote( entity.getNote() );
        sQLRuleDto.setSparefield1( entity.getSparefield1() );
        sQLRuleDto.setSparefield2( entity.getSparefield2() );
        sQLRuleDto.setSparefield3( entity.getSparefield3() );
        sQLRuleDto.setSparefield4( entity.getSparefield4() );
        sQLRuleDto.setSparefield5( entity.getSparefield5() );

        return sQLRuleDto;
    }

    @Override
    public List<SQLRule> toEntity(List<SQLRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SQLRule> list = new ArrayList<SQLRule>( dtoList.size() );
        for ( SQLRuleDto sQLRuleDto : dtoList ) {
            list.add( toEntity( sQLRuleDto ) );
        }

        return list;
    }

    @Override
    public List<SQLRuleDto> toDto(List<SQLRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SQLRuleDto> list = new ArrayList<SQLRuleDto>( entityList.size() );
        for ( SQLRule sQLRule : entityList ) {
            list.add( toDto( sQLRule ) );
        }

        return list;
    }
}
