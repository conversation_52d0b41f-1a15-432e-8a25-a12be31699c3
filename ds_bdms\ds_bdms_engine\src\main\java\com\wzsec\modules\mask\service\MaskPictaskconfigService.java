package com.wzsec.modules.mask.service;


import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigDto;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-04-18
 */
public interface MaskPictaskconfigService {

    /**
     * 查询数据分页
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(MaskPictaskconfigQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     * @param criteria 条件参数
     * @return List<MaskPictaskconfigDto>
     */
    List<MaskPictaskconfigDto> queryAll(MaskPictaskconfigQueryCriteria criteria);

    /**
     * 根据ID查询dto对象
     * @param id ID
     * @return MaskPictaskconfigDto
     */
    MaskPictaskconfigDto findById(Integer id);


    /**
     * 根据ID查询返回实体对象
     * @param id ID
     * @return MaskPictaskconfigDto
     */
    MaskPictaskconfig findMaskPicTaskConfigById(Integer id);

    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(Integer[] ids);


    /**
     * 编辑
     * @param resources /
     */
    void update(MaskPictaskconfig resources);

    /**
     * 根据ID更新任务状态
     *
     * @param id id
     */
    void updateTaskStatus(Integer id, String status);


}