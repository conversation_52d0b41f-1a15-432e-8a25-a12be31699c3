package com.wzsec.modules.sdk.service.mapper;

import com.wzsec.modules.sdk.domain.SdkApplyconfig;
import com.wzsec.modules.sdk.service.dto.SdkApplyconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class SdkApplyconfigMapperImpl implements SdkApplyconfigMapper {

    @Override
    public SdkApplyconfig toEntity(SdkApplyconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        SdkApplyconfig sdkApplyconfig = new SdkApplyconfig();

        sdkApplyconfig.setId( dto.getId() );
        sdkApplyconfig.setSdkid( dto.getSdkid() );
        sdkApplyconfig.setSdkname( dto.getSdkname() );
        sdkApplyconfig.setVersion( dto.getVersion() );
        sdkApplyconfig.setApplyname( dto.getApplyname() );
        sdkApplyconfig.setContactphone( dto.getContactphone() );
        sdkApplyconfig.setApplysystemname( dto.getApplysystemname() );
        sdkApplyconfig.setUseway( dto.getUseway() );
        sdkApplyconfig.setState( dto.getState() );
        sdkApplyconfig.setCreateuser( dto.getCreateuser() );
        sdkApplyconfig.setCreatetime( dto.getCreatetime() );
        sdkApplyconfig.setUpdateuser( dto.getUpdateuser() );
        sdkApplyconfig.setUpdatetime( dto.getUpdatetime() );
        sdkApplyconfig.setSparefield1( dto.getSparefield1() );
        sdkApplyconfig.setSparefield2( dto.getSparefield2() );
        sdkApplyconfig.setSparefield3( dto.getSparefield3() );
        sdkApplyconfig.setSparefield4( dto.getSparefield4() );

        return sdkApplyconfig;
    }

    @Override
    public SdkApplyconfigDto toDto(SdkApplyconfig entity) {
        if ( entity == null ) {
            return null;
        }

        SdkApplyconfigDto sdkApplyconfigDto = new SdkApplyconfigDto();

        sdkApplyconfigDto.setId( entity.getId() );
        sdkApplyconfigDto.setSdkid( entity.getSdkid() );
        sdkApplyconfigDto.setSdkname( entity.getSdkname() );
        sdkApplyconfigDto.setVersion( entity.getVersion() );
        sdkApplyconfigDto.setApplyname( entity.getApplyname() );
        sdkApplyconfigDto.setContactphone( entity.getContactphone() );
        sdkApplyconfigDto.setApplysystemname( entity.getApplysystemname() );
        sdkApplyconfigDto.setUseway( entity.getUseway() );
        sdkApplyconfigDto.setState( entity.getState() );
        sdkApplyconfigDto.setCreateuser( entity.getCreateuser() );
        sdkApplyconfigDto.setCreatetime( entity.getCreatetime() );
        sdkApplyconfigDto.setUpdateuser( entity.getUpdateuser() );
        sdkApplyconfigDto.setUpdatetime( entity.getUpdatetime() );
        sdkApplyconfigDto.setSparefield1( entity.getSparefield1() );
        sdkApplyconfigDto.setSparefield2( entity.getSparefield2() );
        sdkApplyconfigDto.setSparefield3( entity.getSparefield3() );
        sdkApplyconfigDto.setSparefield4( entity.getSparefield4() );

        return sdkApplyconfigDto;
    }

    @Override
    public List<SdkApplyconfig> toEntity(List<SdkApplyconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SdkApplyconfig> list = new ArrayList<SdkApplyconfig>( dtoList.size() );
        for ( SdkApplyconfigDto sdkApplyconfigDto : dtoList ) {
            list.add( toEntity( sdkApplyconfigDto ) );
        }

        return list;
    }

    @Override
    public List<SdkApplyconfigDto> toDto(List<SdkApplyconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SdkApplyconfigDto> list = new ArrayList<SdkApplyconfigDto>( entityList.size() );
        for ( SdkApplyconfig sdkApplyconfig : entityList ) {
            list.add( toDto( sdkApplyconfig ) );
        }

        return list;
    }
}
