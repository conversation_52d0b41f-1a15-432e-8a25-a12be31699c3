package com.wzsec.modules.mask.service.impl;

import com.wzsec.modules.mask.domain.MaskVideotaskconfig;
import com.wzsec.modules.mask.repository.MaskVideotaskconfigRepository;
import com.wzsec.modules.mask.service.MaskVideotaskconfigService;
import com.wzsec.modules.mask.service.dto.MaskVideotaskconfigDto;
import com.wzsec.modules.mask.service.dto.MaskVideotaskconfigQueryCriteria;
import com.wzsec.modules.mask.service.mapper.MaskVideotaskconfigMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-04-21
 */
@Service
//@CacheConfig(cacheNames = "maskVideotaskconfig")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class MaskVideotaskconfigServiceImpl implements MaskVideotaskconfigService {

    private final MaskVideotaskconfigRepository maskVideotaskconfigRepository;

    private final MaskVideotaskconfigMapper maskVideotaskconfigMapper;

    public MaskVideotaskconfigServiceImpl(MaskVideotaskconfigRepository maskVideotaskconfigRepository, MaskVideotaskconfigMapper maskVideotaskconfigMapper) {
        this.maskVideotaskconfigRepository = maskVideotaskconfigRepository;
        this.maskVideotaskconfigMapper = maskVideotaskconfigMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(MaskVideotaskconfigQueryCriteria criteria, Pageable pageable) {
        Page<MaskVideotaskconfig> page = maskVideotaskconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(maskVideotaskconfigMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<MaskVideotaskconfigDto> queryAll(MaskVideotaskconfigQueryCriteria criteria) {
        return maskVideotaskconfigMapper.toDto(maskVideotaskconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public MaskVideotaskconfigDto findById(Integer id) {
        MaskVideotaskconfig maskVideotaskconfig = maskVideotaskconfigRepository.findById(id).orElseGet(MaskVideotaskconfig::new);
        ValidationUtil.isNull(maskVideotaskconfig.getId(), "MaskVideotaskconfig", "id", id);
        return maskVideotaskconfigMapper.toDto(maskVideotaskconfig);
    }

    @Override
    public MaskVideotaskconfig findMaskVideoTaskConfigById(Integer id) {
        MaskVideotaskconfig maskVideotaskconfig = maskVideotaskconfigRepository.findById(id).orElseGet(MaskVideotaskconfig::new);
        ValidationUtil.isNull(maskVideotaskconfig.getId(), "MaskVideotaskconfig", "id", id);
        return maskVideotaskconfig;
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public MaskVideotaskconfigDto create(MaskVideotaskconfig resources) {
        return maskVideotaskconfigMapper.toDto(maskVideotaskconfigRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(MaskVideotaskconfig resources) {
        MaskVideotaskconfig maskVideotaskconfig = maskVideotaskconfigRepository.findById(resources.getId()).orElseGet(MaskVideotaskconfig::new);
        ValidationUtil.isNull(maskVideotaskconfig.getId(), "MaskVideotaskconfig", "id", resources.getId());
        maskVideotaskconfig.copy(resources);
        maskVideotaskconfigRepository.save(maskVideotaskconfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            maskVideotaskconfigRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<MaskVideotaskconfigDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MaskVideotaskconfigDto maskVideotaskconfig : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("任务名", maskVideotaskconfig.getTaskname());
            map.put("原始视频目录", maskVideotaskconfig.getInputdirectory());
            map.put("输入文件格式", maskVideotaskconfig.getInputfileformat());
            map.put("计算资源", maskVideotaskconfig.getComputeresources());
            map.put("脱敏对象", maskVideotaskconfig.getMaskobject());
            map.put("任务状态", maskVideotaskconfig.getState());
            map.put("输出文件格式", maskVideotaskconfig.getOutputfileformat());
            map.put("脱敏后视频或图片目录", maskVideotaskconfig.getOutputdirectory());
            map.put("执行状态", maskVideotaskconfig.getExecutionstate());
            map.put("创建用户", maskVideotaskconfig.getCreateuser());
            map.put("创建时间", maskVideotaskconfig.getCreatetime());
            map.put("更新用户", maskVideotaskconfig.getUpdateuser());
            map.put("更新时间", maskVideotaskconfig.getUpdatetime());
            map.put("备注", maskVideotaskconfig.getRemark());
            map.put("备用字段1", maskVideotaskconfig.getSparefield1());
            map.put("备用字段2", maskVideotaskconfig.getSparefield2());
            map.put("备用字段3", maskVideotaskconfig.getSparefield3());
            map.put("备用字段4", maskVideotaskconfig.getSparefield4());
            map.put("备用字段5", maskVideotaskconfig.getSparefield5());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(Integer id, String status) {
        maskVideotaskconfigRepository.updateTaskStatus(id, status);
    }
}