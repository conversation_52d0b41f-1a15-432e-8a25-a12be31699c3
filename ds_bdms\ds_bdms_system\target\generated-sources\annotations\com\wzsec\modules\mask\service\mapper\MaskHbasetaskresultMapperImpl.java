package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskHbasetaskresult;
import com.wzsec.modules.mask.service.dto.MaskHbasetaskresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskHbasetaskresultMapperImpl implements MaskHbasetaskresultMapper {

    @Override
    public MaskHbasetaskresult toEntity(MaskHbasetaskresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskHbasetaskresult maskHbasetaskresult = new MaskHbasetaskresult();

        maskHbasetaskresult.setId( dto.getId() );
        maskHbasetaskresult.setWorksheet( dto.getWorksheet() );
        maskHbasetaskresult.setBatchnumber( dto.getBatchnumber() );
        maskHbasetaskresult.setDbname( dto.getDbname() );
        maskHbasetaskresult.setTabname( dto.getTabname() );
        maskHbasetaskresult.setStrategyname( dto.getStrategyname() );
        maskHbasetaskresult.setCount( dto.getCount() );
        maskHbasetaskresult.setOutputtype( dto.getOutputtype() );
        maskHbasetaskresult.setOutputtablename( dto.getOutputtablename() );
        maskHbasetaskresult.setDatasplit( dto.getDatasplit() );
        maskHbasetaskresult.setDataoutputdir( dto.getDataoutputdir() );
        maskHbasetaskresult.setJobstarttime( dto.getJobstarttime() );
        maskHbasetaskresult.setJobendtime( dto.getJobendtime() );
        maskHbasetaskresult.setJobtotaltime( dto.getJobtotaltime() );
        maskHbasetaskresult.setJobstatus( dto.getJobstatus() );
        maskHbasetaskresult.setCreateuserid( dto.getCreateuserid() );
        maskHbasetaskresult.setCreatetime( dto.getCreatetime() );
        maskHbasetaskresult.setUpdateuserid( dto.getUpdateuserid() );
        maskHbasetaskresult.setUpdatetime( dto.getUpdatetime() );
        maskHbasetaskresult.setRemark( dto.getRemark() );
        maskHbasetaskresult.setSparefield1( dto.getSparefield1() );
        maskHbasetaskresult.setSparefield2( dto.getSparefield2() );
        maskHbasetaskresult.setSparefield3( dto.getSparefield3() );
        maskHbasetaskresult.setSparefield4( dto.getSparefield4() );

        return maskHbasetaskresult;
    }

    @Override
    public MaskHbasetaskresultDto toDto(MaskHbasetaskresult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskHbasetaskresultDto maskHbasetaskresultDto = new MaskHbasetaskresultDto();

        maskHbasetaskresultDto.setId( entity.getId() );
        maskHbasetaskresultDto.setWorksheet( entity.getWorksheet() );
        maskHbasetaskresultDto.setBatchnumber( entity.getBatchnumber() );
        maskHbasetaskresultDto.setDbname( entity.getDbname() );
        maskHbasetaskresultDto.setTabname( entity.getTabname() );
        maskHbasetaskresultDto.setStrategyname( entity.getStrategyname() );
        maskHbasetaskresultDto.setCount( entity.getCount() );
        maskHbasetaskresultDto.setOutputtype( entity.getOutputtype() );
        maskHbasetaskresultDto.setOutputtablename( entity.getOutputtablename() );
        maskHbasetaskresultDto.setDatasplit( entity.getDatasplit() );
        maskHbasetaskresultDto.setDataoutputdir( entity.getDataoutputdir() );
        maskHbasetaskresultDto.setJobstarttime( entity.getJobstarttime() );
        maskHbasetaskresultDto.setJobendtime( entity.getJobendtime() );
        maskHbasetaskresultDto.setJobtotaltime( entity.getJobtotaltime() );
        maskHbasetaskresultDto.setJobstatus( entity.getJobstatus() );
        maskHbasetaskresultDto.setCreateuserid( entity.getCreateuserid() );
        maskHbasetaskresultDto.setCreatetime( entity.getCreatetime() );
        maskHbasetaskresultDto.setUpdateuserid( entity.getUpdateuserid() );
        maskHbasetaskresultDto.setUpdatetime( entity.getUpdatetime() );
        maskHbasetaskresultDto.setRemark( entity.getRemark() );
        maskHbasetaskresultDto.setSparefield1( entity.getSparefield1() );
        maskHbasetaskresultDto.setSparefield2( entity.getSparefield2() );
        maskHbasetaskresultDto.setSparefield3( entity.getSparefield3() );
        maskHbasetaskresultDto.setSparefield4( entity.getSparefield4() );

        return maskHbasetaskresultDto;
    }

    @Override
    public List<MaskHbasetaskresult> toEntity(List<MaskHbasetaskresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskHbasetaskresult> list = new ArrayList<MaskHbasetaskresult>( dtoList.size() );
        for ( MaskHbasetaskresultDto maskHbasetaskresultDto : dtoList ) {
            list.add( toEntity( maskHbasetaskresultDto ) );
        }

        return list;
    }

    @Override
    public List<MaskHbasetaskresultDto> toDto(List<MaskHbasetaskresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskHbasetaskresultDto> list = new ArrayList<MaskHbasetaskresultDto>( entityList.size() );
        for ( MaskHbasetaskresult maskHbasetaskresult : entityList ) {
            list.add( toDto( maskHbasetaskresult ) );
        }

        return list;
    }
}
