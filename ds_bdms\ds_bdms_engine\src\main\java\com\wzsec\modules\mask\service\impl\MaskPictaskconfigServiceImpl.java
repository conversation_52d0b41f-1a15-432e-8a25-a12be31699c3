package com.wzsec.modules.mask.service.impl;

import com.wzsec.modules.mask.domain.MaskPictaskconfig;
import com.wzsec.modules.mask.repository.MaskPictaskconfigRepository;
import com.wzsec.modules.mask.service.MaskPictaskconfigService;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigDto;
import com.wzsec.modules.mask.service.dto.MaskPictaskconfigQueryCriteria;
import com.wzsec.modules.mask.service.mapper.MaskPictaskconfigMapper;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-04-18
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class MaskPictaskconfigServiceImpl implements MaskPictaskconfigService {

    @Autowired
    MaskPictaskconfigRepository maskPictaskconfigRepository;

    @Autowired
    MaskPictaskconfigMapper maskPictaskconfigMapper;

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(MaskPictaskconfigQueryCriteria criteria, Pageable pageable) {
        Page<MaskPictaskconfig> page = maskPictaskconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(maskPictaskconfigMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<MaskPictaskconfigDto> queryAll(MaskPictaskconfigQueryCriteria criteria) {
        return maskPictaskconfigMapper.toDto(maskPictaskconfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public MaskPictaskconfigDto findById(Integer id) {
        MaskPictaskconfig maskPictaskconfig = maskPictaskconfigRepository.findById(id).orElseGet(MaskPictaskconfig::new);
        ValidationUtil.isNull(maskPictaskconfig.getId(), "MaskPictaskconfig", "id", id);
        return maskPictaskconfigMapper.toDto(maskPictaskconfig);
    }

    @Override
    public MaskPictaskconfig findMaskPicTaskConfigById(Integer id) {
        MaskPictaskconfig maskPictaskconfig = maskPictaskconfigRepository.findById(id).orElseGet(MaskPictaskconfig::new);
        ValidationUtil.isNull(maskPictaskconfig.getId(), "MaskPictaskconfig", "id", id);
        return maskPictaskconfig;
    }


    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            maskPictaskconfigRepository.deleteById(id);
        }
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(MaskPictaskconfig resources) {
        MaskPictaskconfig maskPictaskconfig = maskPictaskconfigRepository.findById(resources.getId()).orElseGet(MaskPictaskconfig::new);
        ValidationUtil.isNull(maskPictaskconfig.getId(), "MaskPictaskconfig", "id", resources.getId());
        maskPictaskconfig.copy(resources);
        maskPictaskconfigRepository.save(maskPictaskconfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTaskStatus(Integer id, String status) {
        maskPictaskconfigRepository.updateTaskStatus(id, status);
    }


}