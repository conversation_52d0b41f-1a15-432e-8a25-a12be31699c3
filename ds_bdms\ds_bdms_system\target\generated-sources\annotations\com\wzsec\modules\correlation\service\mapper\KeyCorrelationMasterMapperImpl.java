package com.wzsec.modules.correlation.service.mapper;

import com.wzsec.modules.correlation.domain.KeyCorrelationMaster;
import com.wzsec.modules.correlation.service.dto.KeyCorrelationMasterDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class KeyCorrelationMasterMapperImpl implements KeyCorrelationMasterMapper {

    @Override
    public KeyCorrelationMaster toEntity(KeyCorrelationMasterDto dto) {
        if ( dto == null ) {
            return null;
        }

        KeyCorrelationMaster keyCorrelationMaster = new KeyCorrelationMaster();

        keyCorrelationMaster.setId( dto.getId() );
        keyCorrelationMaster.setMfid( dto.getMfid() );
        keyCorrelationMaster.setMtid( dto.getMtid() );
        keyCorrelationMaster.setSourceid( dto.getSourceid() );
        keyCorrelationMaster.setCreateuser( dto.getCreateuser() );
        keyCorrelationMaster.setCreatetime( dto.getCreatetime() );
        keyCorrelationMaster.setUpdateuser( dto.getUpdateuser() );
        keyCorrelationMaster.setUpdatetime( dto.getUpdatetime() );

        return keyCorrelationMaster;
    }

    @Override
    public KeyCorrelationMasterDto toDto(KeyCorrelationMaster entity) {
        if ( entity == null ) {
            return null;
        }

        KeyCorrelationMasterDto keyCorrelationMasterDto = new KeyCorrelationMasterDto();

        keyCorrelationMasterDto.setId( entity.getId() );
        keyCorrelationMasterDto.setMfid( entity.getMfid() );
        keyCorrelationMasterDto.setMtid( entity.getMtid() );
        keyCorrelationMasterDto.setSourceid( entity.getSourceid() );
        keyCorrelationMasterDto.setCreateuser( entity.getCreateuser() );
        keyCorrelationMasterDto.setCreatetime( entity.getCreatetime() );
        keyCorrelationMasterDto.setUpdateuser( entity.getUpdateuser() );
        keyCorrelationMasterDto.setUpdatetime( entity.getUpdatetime() );

        return keyCorrelationMasterDto;
    }

    @Override
    public List<KeyCorrelationMaster> toEntity(List<KeyCorrelationMasterDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KeyCorrelationMaster> list = new ArrayList<KeyCorrelationMaster>( dtoList.size() );
        for ( KeyCorrelationMasterDto keyCorrelationMasterDto : dtoList ) {
            list.add( toEntity( keyCorrelationMasterDto ) );
        }

        return list;
    }

    @Override
    public List<KeyCorrelationMasterDto> toDto(List<KeyCorrelationMaster> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KeyCorrelationMasterDto> list = new ArrayList<KeyCorrelationMasterDto>( entityList.size() );
        for ( KeyCorrelationMaster keyCorrelationMaster : entityList ) {
            list.add( toDto( keyCorrelationMaster ) );
        }

        return list;
    }
}
