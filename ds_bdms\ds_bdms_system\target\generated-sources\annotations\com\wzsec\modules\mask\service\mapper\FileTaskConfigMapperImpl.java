package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.FileTaskConfig;
import com.wzsec.modules.mask.service.dto.FileTaskConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FileTaskConfigMapperImpl implements FileTaskConfigMapper {

    @Override
    public FileTaskConfig toEntity(FileTaskConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        FileTaskConfig fileTaskConfig = new FileTaskConfig();

        fileTaskConfig.setId( dto.getId() );
        fileTaskConfig.setTaskname( dto.getTaskname() );
        fileTaskConfig.setInputdatasourceid( dto.getInputdatasourceid() );
        fileTaskConfig.setInputpath( dto.getInputpath() );
        fileTaskConfig.setInputfiletype( dto.getInputfiletype() );
        fileTaskConfig.setSplitstr( dto.getSplitstr() );
        fileTaskConfig.setState( dto.getState() );
        fileTaskConfig.setOutputtype( dto.getOutputtype() );
        fileTaskConfig.setOutputdatasourceid( dto.getOutputdatasourceid() );
        fileTaskConfig.setTablename( dto.getTablename() );
        fileTaskConfig.setOutputdirectory( dto.getOutputdirectory() );
        fileTaskConfig.setOutputfiletype( dto.getOutputfiletype() );
        fileTaskConfig.setSubmitmethod( dto.getSubmitmethod() );
        fileTaskConfig.setCron( dto.getCron() );
        fileTaskConfig.setExecutionstate( dto.getExecutionstate() );
        fileTaskConfig.setExtractfield( dto.getExtractfield() );
        fileTaskConfig.setMaskstrategystr( dto.getMaskstrategystr() );
        fileTaskConfig.setIswatermark( dto.getIswatermark() );
        fileTaskConfig.setDataprovider( dto.getDataprovider() );
        fileTaskConfig.setDatause( dto.getDatause() );
        fileTaskConfig.setCreatetime( dto.getCreatetime() );
        fileTaskConfig.setUpdatetime( dto.getUpdatetime() );
        fileTaskConfig.setRemark( dto.getRemark() );
        fileTaskConfig.setTaskexecuteengine( dto.getTaskexecuteengine() );
        fileTaskConfig.setSparefield1( dto.getSparefield1() );
        fileTaskConfig.setSparefield2( dto.getSparefield2() );
        fileTaskConfig.setSparefield3( dto.getSparefield3() );
        fileTaskConfig.setSparefield4( dto.getSparefield4() );
        fileTaskConfig.setSparefield5( dto.getSparefield5() );
        fileTaskConfig.setStrategyid( dto.getStrategyid() );
        fileTaskConfig.setCreateuser( dto.getCreateuser() );
        fileTaskConfig.setUpdateuser( dto.getUpdateuser() );
        fileTaskConfig.setEnablemoderation( dto.getEnablemoderation() );
        fileTaskConfig.setApprovalstatus( dto.getApprovalstatus() );
        fileTaskConfig.setApprover( dto.getApprover() );
        fileTaskConfig.setApprovaltime( dto.getApprovaltime() );

        return fileTaskConfig;
    }

    @Override
    public FileTaskConfigDto toDto(FileTaskConfig entity) {
        if ( entity == null ) {
            return null;
        }

        FileTaskConfigDto fileTaskConfigDto = new FileTaskConfigDto();

        fileTaskConfigDto.setId( entity.getId() );
        fileTaskConfigDto.setTaskname( entity.getTaskname() );
        fileTaskConfigDto.setInputdatasourceid( entity.getInputdatasourceid() );
        fileTaskConfigDto.setInputpath( entity.getInputpath() );
        fileTaskConfigDto.setInputfiletype( entity.getInputfiletype() );
        fileTaskConfigDto.setSplitstr( entity.getSplitstr() );
        fileTaskConfigDto.setState( entity.getState() );
        fileTaskConfigDto.setOutputtype( entity.getOutputtype() );
        fileTaskConfigDto.setOutputdatasourceid( entity.getOutputdatasourceid() );
        fileTaskConfigDto.setTablename( entity.getTablename() );
        fileTaskConfigDto.setOutputdirectory( entity.getOutputdirectory() );
        fileTaskConfigDto.setOutputfiletype( entity.getOutputfiletype() );
        fileTaskConfigDto.setSubmitmethod( entity.getSubmitmethod() );
        fileTaskConfigDto.setCron( entity.getCron() );
        fileTaskConfigDto.setExecutionstate( entity.getExecutionstate() );
        fileTaskConfigDto.setExtractfield( entity.getExtractfield() );
        fileTaskConfigDto.setMaskstrategystr( entity.getMaskstrategystr() );
        fileTaskConfigDto.setIswatermark( entity.getIswatermark() );
        fileTaskConfigDto.setDataprovider( entity.getDataprovider() );
        fileTaskConfigDto.setDatause( entity.getDatause() );
        fileTaskConfigDto.setCreatetime( entity.getCreatetime() );
        fileTaskConfigDto.setUpdatetime( entity.getUpdatetime() );
        fileTaskConfigDto.setRemark( entity.getRemark() );
        fileTaskConfigDto.setTaskexecuteengine( entity.getTaskexecuteengine() );
        fileTaskConfigDto.setSparefield1( entity.getSparefield1() );
        fileTaskConfigDto.setSparefield2( entity.getSparefield2() );
        fileTaskConfigDto.setSparefield3( entity.getSparefield3() );
        fileTaskConfigDto.setSparefield4( entity.getSparefield4() );
        fileTaskConfigDto.setSparefield5( entity.getSparefield5() );
        fileTaskConfigDto.setStrategyid( entity.getStrategyid() );
        fileTaskConfigDto.setCreateuser( entity.getCreateuser() );
        fileTaskConfigDto.setUpdateuser( entity.getUpdateuser() );
        fileTaskConfigDto.setEnablemoderation( entity.getEnablemoderation() );
        fileTaskConfigDto.setApprovalstatus( entity.getApprovalstatus() );
        fileTaskConfigDto.setApprover( entity.getApprover() );
        fileTaskConfigDto.setApprovaltime( entity.getApprovaltime() );

        return fileTaskConfigDto;
    }

    @Override
    public List<FileTaskConfig> toEntity(List<FileTaskConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FileTaskConfig> list = new ArrayList<FileTaskConfig>( dtoList.size() );
        for ( FileTaskConfigDto fileTaskConfigDto : dtoList ) {
            list.add( toEntity( fileTaskConfigDto ) );
        }

        return list;
    }

    @Override
    public List<FileTaskConfigDto> toDto(List<FileTaskConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FileTaskConfigDto> list = new ArrayList<FileTaskConfigDto>( entityList.size() );
        for ( FileTaskConfig fileTaskConfig : entityList ) {
            list.add( toDto( fileTaskConfig ) );
        }

        return list;
    }
}
