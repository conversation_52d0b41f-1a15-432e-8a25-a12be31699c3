package com.wzsec.modules.source.service.mapper;

import com.wzsec.modules.source.domain.DatasourceScanner;
import com.wzsec.modules.source.service.dto.HostScannerDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DatasourceScanMapperImpl implements DatasourceScanMapper {

    @Override
    public DatasourceScanner toEntity(HostScannerDto dto) {
        if ( dto == null ) {
            return null;
        }

        DatasourceScanner datasourceScanner = new DatasourceScanner();

        datasourceScanner.setId( dto.getId() );
        datasourceScanner.setIp( dto.getIp() );
        datasourceScanner.setPort( dto.getPort() );
        datasourceScanner.setService( dto.getService() );
        datasourceScanner.setScantime( dto.getScantime() );
        datasourceScanner.setSparefield1( dto.getSparefield1() );
        datasourceScanner.setSparefield2( dto.getSparefield2() );

        return datasourceScanner;
    }

    @Override
    public HostScannerDto toDto(DatasourceScanner entity) {
        if ( entity == null ) {
            return null;
        }

        HostScannerDto hostScannerDto = new HostScannerDto();

        hostScannerDto.setId( entity.getId() );
        hostScannerDto.setIp( entity.getIp() );
        hostScannerDto.setPort( entity.getPort() );
        hostScannerDto.setService( entity.getService() );
        hostScannerDto.setScantime( entity.getScantime() );
        hostScannerDto.setSparefield1( entity.getSparefield1() );
        hostScannerDto.setSparefield2( entity.getSparefield2() );

        return hostScannerDto;
    }

    @Override
    public List<DatasourceScanner> toEntity(List<HostScannerDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DatasourceScanner> list = new ArrayList<DatasourceScanner>( dtoList.size() );
        for ( HostScannerDto hostScannerDto : dtoList ) {
            list.add( toEntity( hostScannerDto ) );
        }

        return list;
    }

    @Override
    public List<HostScannerDto> toDto(List<DatasourceScanner> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<HostScannerDto> list = new ArrayList<HostScannerDto>( entityList.size() );
        for ( DatasourceScanner datasourceScanner : entityList ) {
            list.add( toDto( datasourceScanner ) );
        }

        return list;
    }
}
