package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.StatisticstaskDetail;
import com.wzsec.modules.statistics.service.dto.StatisticstaskDetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class StatisticstaskDetailMapperImpl implements StatisticstaskDetailMapper {

    @Override
    public StatisticstaskDetail toEntity(StatisticstaskDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        StatisticstaskDetail statisticstaskDetail = new StatisticstaskDetail();

        statisticstaskDetail.setId( dto.getId() );
        statisticstaskDetail.setTaskname( dto.getTaskname() );
        statisticstaskDetail.setSrcname( dto.getSrcname() );
        statisticstaskDetail.setTablename( dto.getTablename() );
        statisticstaskDetail.setFieldcount( dto.getFieldcount() );
        statisticstaskDetail.setDatacount( dto.getDatacount() );
        statisticstaskDetail.setCreateuser( dto.getCreateuser() );
        statisticstaskDetail.setCreatetime( dto.getCreatetime() );
        statisticstaskDetail.setSparefield1( dto.getSparefield1() );
        statisticstaskDetail.setSparefield2( dto.getSparefield2() );
        statisticstaskDetail.setSparefield3( dto.getSparefield3() );

        return statisticstaskDetail;
    }

    @Override
    public StatisticstaskDetailDto toDto(StatisticstaskDetail entity) {
        if ( entity == null ) {
            return null;
        }

        StatisticstaskDetailDto statisticstaskDetailDto = new StatisticstaskDetailDto();

        statisticstaskDetailDto.setId( entity.getId() );
        statisticstaskDetailDto.setTaskname( entity.getTaskname() );
        statisticstaskDetailDto.setSrcname( entity.getSrcname() );
        statisticstaskDetailDto.setTablename( entity.getTablename() );
        statisticstaskDetailDto.setFieldcount( entity.getFieldcount() );
        statisticstaskDetailDto.setDatacount( entity.getDatacount() );
        statisticstaskDetailDto.setCreateuser( entity.getCreateuser() );
        statisticstaskDetailDto.setCreatetime( entity.getCreatetime() );
        statisticstaskDetailDto.setSparefield1( entity.getSparefield1() );
        statisticstaskDetailDto.setSparefield2( entity.getSparefield2() );
        statisticstaskDetailDto.setSparefield3( entity.getSparefield3() );

        return statisticstaskDetailDto;
    }

    @Override
    public List<StatisticstaskDetail> toEntity(List<StatisticstaskDetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<StatisticstaskDetail> list = new ArrayList<StatisticstaskDetail>( dtoList.size() );
        for ( StatisticstaskDetailDto statisticstaskDetailDto : dtoList ) {
            list.add( toEntity( statisticstaskDetailDto ) );
        }

        return list;
    }

    @Override
    public List<StatisticstaskDetailDto> toDto(List<StatisticstaskDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<StatisticstaskDetailDto> list = new ArrayList<StatisticstaskDetailDto>( entityList.size() );
        for ( StatisticstaskDetail statisticstaskDetail : entityList ) {
            list.add( toDto( statisticstaskDetail ) );
        }

        return list;
    }
}
