package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.TasksynrecordOutline;
import com.wzsec.modules.statistics.service.dto.TasksynrecordOutlineDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TasksynrecordOutlineMapperImpl implements TasksynrecordOutlineMapper {

    @Override
    public TasksynrecordOutline toEntity(TasksynrecordOutlineDto dto) {
        if ( dto == null ) {
            return null;
        }

        TasksynrecordOutline tasksynrecordOutline = new TasksynrecordOutline();

        tasksynrecordOutline.setId( dto.getId() );
        tasksynrecordOutline.setTaskname( dto.getTaskname() );
        tasksynrecordOutline.setTasknumber( dto.getTasknumber() );
        tasksynrecordOutline.setSrcname( dto.getSrcname() );
        tasksynrecordOutline.setSourcetype( dto.getSourcetype() );
        tasksynrecordOutline.setDbname( dto.getDbname() );
        tasksynrecordOutline.setSynoutline( dto.getSynoutline() );
        tasksynrecordOutline.setCreateuser( dto.getCreateuser() );
        tasksynrecordOutline.setCreatetime( dto.getCreatetime() );
        tasksynrecordOutline.setSparefield1( dto.getSparefield1() );
        tasksynrecordOutline.setSparefield2( dto.getSparefield2() );
        tasksynrecordOutline.setSparefield3( dto.getSparefield3() );

        return tasksynrecordOutline;
    }

    @Override
    public TasksynrecordOutlineDto toDto(TasksynrecordOutline entity) {
        if ( entity == null ) {
            return null;
        }

        TasksynrecordOutlineDto tasksynrecordOutlineDto = new TasksynrecordOutlineDto();

        tasksynrecordOutlineDto.setId( entity.getId() );
        tasksynrecordOutlineDto.setTaskname( entity.getTaskname() );
        tasksynrecordOutlineDto.setTasknumber( entity.getTasknumber() );
        tasksynrecordOutlineDto.setSrcname( entity.getSrcname() );
        tasksynrecordOutlineDto.setSourcetype( entity.getSourcetype() );
        tasksynrecordOutlineDto.setDbname( entity.getDbname() );
        tasksynrecordOutlineDto.setSynoutline( entity.getSynoutline() );
        tasksynrecordOutlineDto.setCreateuser( entity.getCreateuser() );
        tasksynrecordOutlineDto.setCreatetime( entity.getCreatetime() );
        tasksynrecordOutlineDto.setSparefield1( entity.getSparefield1() );
        tasksynrecordOutlineDto.setSparefield2( entity.getSparefield2() );
        tasksynrecordOutlineDto.setSparefield3( entity.getSparefield3() );

        return tasksynrecordOutlineDto;
    }

    @Override
    public List<TasksynrecordOutline> toEntity(List<TasksynrecordOutlineDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TasksynrecordOutline> list = new ArrayList<TasksynrecordOutline>( dtoList.size() );
        for ( TasksynrecordOutlineDto tasksynrecordOutlineDto : dtoList ) {
            list.add( toEntity( tasksynrecordOutlineDto ) );
        }

        return list;
    }

    @Override
    public List<TasksynrecordOutlineDto> toDto(List<TasksynrecordOutline> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TasksynrecordOutlineDto> list = new ArrayList<TasksynrecordOutlineDto>( entityList.size() );
        for ( TasksynrecordOutline tasksynrecordOutline : entityList ) {
            list.add( toDto( tasksynrecordOutline ) );
        }

        return list;
    }
}
