package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskStrategyTableAllSub;
import com.wzsec.modules.mask.service.dto.MaskStrategyTableAllSubDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:03+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyTableAllSubMapperImpl implements MaskStrategyTableAllSubMapper {

    @Override
    public MaskStrategyTableAllSub toEntity(MaskStrategyTableAllSubDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyTableAllSub maskStrategyTableAllSub = new MaskStrategyTableAllSub();

        maskStrategyTableAllSub.setId( dto.getId() );
        maskStrategyTableAllSub.setStrategyid( dto.getStrategyid() );
        maskStrategyTableAllSub.setDataname( dto.getDataname() );
        maskStrategyTableAllSub.setMaskruleid( dto.getMaskruleid() );
        maskStrategyTableAllSub.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyTableAllSub.setParam( dto.getParam() );
        maskStrategyTableAllSub.setSecretkey( dto.getSecretkey() );
        maskStrategyTableAllSub.setSparefield1( dto.getSparefield1() );
        maskStrategyTableAllSub.setSparefield2( dto.getSparefield2() );
        maskStrategyTableAllSub.setSparefield3( dto.getSparefield3() );
        maskStrategyTableAllSub.setSparefield4( dto.getSparefield4() );

        return maskStrategyTableAllSub;
    }

    @Override
    public MaskStrategyTableAllSubDto toDto(MaskStrategyTableAllSub entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyTableAllSubDto maskStrategyTableAllSubDto = new MaskStrategyTableAllSubDto();

        maskStrategyTableAllSubDto.setId( entity.getId() );
        maskStrategyTableAllSubDto.setStrategyid( entity.getStrategyid() );
        maskStrategyTableAllSubDto.setDataname( entity.getDataname() );
        maskStrategyTableAllSubDto.setMaskruleid( entity.getMaskruleid() );
        maskStrategyTableAllSubDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyTableAllSubDto.setParam( entity.getParam() );
        maskStrategyTableAllSubDto.setSecretkey( entity.getSecretkey() );
        maskStrategyTableAllSubDto.setSparefield1( entity.getSparefield1() );
        maskStrategyTableAllSubDto.setSparefield2( entity.getSparefield2() );
        maskStrategyTableAllSubDto.setSparefield3( entity.getSparefield3() );
        maskStrategyTableAllSubDto.setSparefield4( entity.getSparefield4() );

        return maskStrategyTableAllSubDto;
    }

    @Override
    public List<MaskStrategyTableAllSub> toEntity(List<MaskStrategyTableAllSubDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyTableAllSub> list = new ArrayList<MaskStrategyTableAllSub>( dtoList.size() );
        for ( MaskStrategyTableAllSubDto maskStrategyTableAllSubDto : dtoList ) {
            list.add( toEntity( maskStrategyTableAllSubDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyTableAllSubDto> toDto(List<MaskStrategyTableAllSub> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyTableAllSubDto> list = new ArrayList<MaskStrategyTableAllSubDto>( entityList.size() );
        for ( MaskStrategyTableAllSub maskStrategyTableAllSub : entityList ) {
            list.add( toDto( maskStrategyTableAllSub ) );
        }

        return list;
    }
}
