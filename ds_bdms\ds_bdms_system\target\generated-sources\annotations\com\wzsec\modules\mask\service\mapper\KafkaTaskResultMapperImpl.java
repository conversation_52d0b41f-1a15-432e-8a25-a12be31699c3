package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.KafkaTaskResult;
import com.wzsec.modules.mask.service.dto.KafkaTaskResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:05:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class KafkaTaskResultMapperImpl implements KafkaTaskResultMapper {

    @Override
    public KafkaTaskResult toEntity(KafkaTaskResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        KafkaTaskResult kafkaTaskResult = new KafkaTaskResult();

        kafkaTaskResult.setId( dto.getId() );
        kafkaTaskResult.setTaskname( dto.getTaskname() );
        kafkaTaskResult.setDbname( dto.getDbname() );
        kafkaTaskResult.setTabname( dto.getTabname() );
        kafkaTaskResult.setMaskline( dto.getMaskline() );
        kafkaTaskResult.setStrategyname( dto.getStrategyname() );
        kafkaTaskResult.setStrategydetail( dto.getStrategydetail() );
        kafkaTaskResult.setRediskey( dto.getRediskey() );
        kafkaTaskResult.setTaskstatus( dto.getTaskstatus() );
        kafkaTaskResult.setBeforemaskdata( dto.getBeforemaskdata() );
        kafkaTaskResult.setAftermaskdata( dto.getAftermaskdata() );
        kafkaTaskResult.setStarttime( dto.getStarttime() );
        kafkaTaskResult.setEndtime( dto.getEndtime() );
        kafkaTaskResult.setSparefield1( dto.getSparefield1() );
        kafkaTaskResult.setSparefield2( dto.getSparefield2() );
        kafkaTaskResult.setSparefield3( dto.getSparefield3() );
        kafkaTaskResult.setSparefield4( dto.getSparefield4() );
        kafkaTaskResult.setSparefield5( dto.getSparefield5() );

        return kafkaTaskResult;
    }

    @Override
    public KafkaTaskResultDto toDto(KafkaTaskResult entity) {
        if ( entity == null ) {
            return null;
        }

        KafkaTaskResultDto kafkaTaskResultDto = new KafkaTaskResultDto();

        kafkaTaskResultDto.setId( entity.getId() );
        kafkaTaskResultDto.setTaskname( entity.getTaskname() );
        kafkaTaskResultDto.setDbname( entity.getDbname() );
        kafkaTaskResultDto.setTabname( entity.getTabname() );
        kafkaTaskResultDto.setMaskline( entity.getMaskline() );
        kafkaTaskResultDto.setStrategyname( entity.getStrategyname() );
        kafkaTaskResultDto.setStrategydetail( entity.getStrategydetail() );
        kafkaTaskResultDto.setRediskey( entity.getRediskey() );
        kafkaTaskResultDto.setTaskstatus( entity.getTaskstatus() );
        kafkaTaskResultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        kafkaTaskResultDto.setAftermaskdata( entity.getAftermaskdata() );
        kafkaTaskResultDto.setStarttime( entity.getStarttime() );
        kafkaTaskResultDto.setEndtime( entity.getEndtime() );
        kafkaTaskResultDto.setSparefield1( entity.getSparefield1() );
        kafkaTaskResultDto.setSparefield2( entity.getSparefield2() );
        kafkaTaskResultDto.setSparefield3( entity.getSparefield3() );
        kafkaTaskResultDto.setSparefield4( entity.getSparefield4() );
        kafkaTaskResultDto.setSparefield5( entity.getSparefield5() );

        return kafkaTaskResultDto;
    }

    @Override
    public List<KafkaTaskResult> toEntity(List<KafkaTaskResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<KafkaTaskResult> list = new ArrayList<KafkaTaskResult>( dtoList.size() );
        for ( KafkaTaskResultDto kafkaTaskResultDto : dtoList ) {
            list.add( toEntity( kafkaTaskResultDto ) );
        }

        return list;
    }

    @Override
    public List<KafkaTaskResultDto> toDto(List<KafkaTaskResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<KafkaTaskResultDto> list = new ArrayList<KafkaTaskResultDto>( entityList.size() );
        for ( KafkaTaskResult kafkaTaskResult : entityList ) {
            list.add( toDto( kafkaTaskResult ) );
        }

        return list;
    }
}
