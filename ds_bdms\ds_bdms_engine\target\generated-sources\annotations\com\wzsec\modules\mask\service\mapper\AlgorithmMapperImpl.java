package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.Algorithm;
import com.wzsec.modules.mask.service.dto.AlgorithmDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:23+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class AlgorithmMapperImpl implements AlgorithmMapper {

    @Override
    public Algorithm toEntity(AlgorithmDto dto) {
        if ( dto == null ) {
            return null;
        }

        Algorithm algorithm = new Algorithm();

        algorithm.setId( dto.getId() );
        algorithm.setAlgorithmname( dto.getAlgorithmname() );
        algorithm.setAlgenglishname( dto.getAlgenglishname() );
        algorithm.setAlgorithmdesc( dto.getAlgorithmdesc() );
        algorithm.setClassname( dto.getClassname() );
        algorithm.setMethodname( dto.getMethodname() );
        algorithm.setTimerate( dto.getTimerate() );
        algorithm.setSpaceexpand( dto.getSpaceexpand() );
        algorithm.setSafetylevel( dto.getSafetylevel() );
        algorithm.setParamexample( dto.getParamexample() );
        algorithm.setParamdesc( dto.getParamdesc() );
        algorithm.setParamnum( dto.getParamnum() );
        algorithm.setSecretexample( dto.getSecretexample() );
        algorithm.setSecretdesc( dto.getSecretdesc() );
        algorithm.setFuncname( dto.getFuncname() );
        algorithm.setFuncnamepath( dto.getFuncnamepath() );
        algorithm.setJarname( dto.getJarname() );
        algorithm.setFlag( dto.getFlag() );
        algorithm.setKind( dto.getKind() );
        algorithm.setIsreversible( dto.getIsreversible() );
        algorithm.setCreateuser( dto.getCreateuser() );
        algorithm.setCreatetime( dto.getCreatetime() );
        algorithm.setUpdateuser( dto.getUpdateuser() );
        algorithm.setUpdatetime( dto.getUpdatetime() );
        algorithm.setMemo( dto.getMemo() );
        algorithm.setVersion( dto.getVersion() );
        algorithm.setRealizesource( dto.getRealizesource() );
        algorithm.setSparefield1( dto.getSparefield1() );
        algorithm.setSparefield2( dto.getSparefield2() );
        algorithm.setSparefield3( dto.getSparefield3() );
        algorithm.setSparefield4( dto.getSparefield4() );
        algorithm.setSparefield5( dto.getSparefield5() );

        return algorithm;
    }

    @Override
    public AlgorithmDto toDto(Algorithm entity) {
        if ( entity == null ) {
            return null;
        }

        AlgorithmDto algorithmDto = new AlgorithmDto();

        algorithmDto.setId( entity.getId() );
        algorithmDto.setAlgorithmname( entity.getAlgorithmname() );
        algorithmDto.setAlgenglishname( entity.getAlgenglishname() );
        algorithmDto.setAlgorithmdesc( entity.getAlgorithmdesc() );
        algorithmDto.setClassname( entity.getClassname() );
        algorithmDto.setMethodname( entity.getMethodname() );
        algorithmDto.setTimerate( entity.getTimerate() );
        algorithmDto.setSpaceexpand( entity.getSpaceexpand() );
        algorithmDto.setSafetylevel( entity.getSafetylevel() );
        algorithmDto.setParamexample( entity.getParamexample() );
        algorithmDto.setParamdesc( entity.getParamdesc() );
        algorithmDto.setParamnum( entity.getParamnum() );
        algorithmDto.setSecretexample( entity.getSecretexample() );
        algorithmDto.setSecretdesc( entity.getSecretdesc() );
        algorithmDto.setFuncname( entity.getFuncname() );
        algorithmDto.setFuncnamepath( entity.getFuncnamepath() );
        algorithmDto.setJarname( entity.getJarname() );
        algorithmDto.setFlag( entity.getFlag() );
        algorithmDto.setKind( entity.getKind() );
        algorithmDto.setIsreversible( entity.getIsreversible() );
        algorithmDto.setCreateuser( entity.getCreateuser() );
        algorithmDto.setCreatetime( entity.getCreatetime() );
        algorithmDto.setUpdateuser( entity.getUpdateuser() );
        algorithmDto.setUpdatetime( entity.getUpdatetime() );
        algorithmDto.setMemo( entity.getMemo() );
        algorithmDto.setVersion( entity.getVersion() );
        algorithmDto.setRealizesource( entity.getRealizesource() );
        algorithmDto.setSparefield1( entity.getSparefield1() );
        algorithmDto.setSparefield2( entity.getSparefield2() );
        algorithmDto.setSparefield3( entity.getSparefield3() );
        algorithmDto.setSparefield4( entity.getSparefield4() );
        algorithmDto.setSparefield5( entity.getSparefield5() );

        return algorithmDto;
    }

    @Override
    public List<Algorithm> toEntity(List<AlgorithmDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Algorithm> list = new ArrayList<Algorithm>( dtoList.size() );
        for ( AlgorithmDto algorithmDto : dtoList ) {
            list.add( toEntity( algorithmDto ) );
        }

        return list;
    }

    @Override
    public List<AlgorithmDto> toDto(List<Algorithm> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AlgorithmDto> list = new ArrayList<AlgorithmDto>( entityList.size() );
        for ( Algorithm algorithm : entityList ) {
            list.add( toDto( algorithm ) );
        }

        return list;
    }
}
