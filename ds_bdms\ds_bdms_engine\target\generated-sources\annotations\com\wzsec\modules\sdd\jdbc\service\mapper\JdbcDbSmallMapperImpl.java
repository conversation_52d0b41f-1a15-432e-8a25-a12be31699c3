package com.wzsec.modules.sdd.jdbc.service.mapper;

import com.wzsec.modules.sdd.jdbc.domain.JdbcDb;
import com.wzsec.modules.sdd.jdbc.service.dto.JdbcDbSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class JdbcDbSmallMapperImpl implements JdbcDbSmallMapper {

    @Override
    public JdbcDb toEntity(JdbcDbSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        JdbcDb jdbcDb = new JdbcDb();

        jdbcDb.setId( dto.getId() );
        jdbcDb.setLogicdbename( dto.getLogicdbename() );
        jdbcDb.setLogicdbcname( dto.getLogicdbcname() );

        return jdbcDb;
    }

    @Override
    public JdbcDbSmallDto toDto(JdbcDb entity) {
        if ( entity == null ) {
            return null;
        }

        JdbcDbSmallDto jdbcDbSmallDto = new JdbcDbSmallDto();

        jdbcDbSmallDto.setId( entity.getId() );
        jdbcDbSmallDto.setLogicdbename( entity.getLogicdbename() );
        jdbcDbSmallDto.setLogicdbcname( entity.getLogicdbcname() );

        return jdbcDbSmallDto;
    }

    @Override
    public List<JdbcDb> toEntity(List<JdbcDbSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<JdbcDb> list = new ArrayList<JdbcDb>( dtoList.size() );
        for ( JdbcDbSmallDto jdbcDbSmallDto : dtoList ) {
            list.add( toEntity( jdbcDbSmallDto ) );
        }

        return list;
    }

    @Override
    public List<JdbcDbSmallDto> toDto(List<JdbcDb> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JdbcDbSmallDto> list = new ArrayList<JdbcDbSmallDto>( entityList.size() );
        for ( JdbcDb jdbcDb : entityList ) {
            list.add( toDto( jdbcDb ) );
        }

        return list;
    }
}
