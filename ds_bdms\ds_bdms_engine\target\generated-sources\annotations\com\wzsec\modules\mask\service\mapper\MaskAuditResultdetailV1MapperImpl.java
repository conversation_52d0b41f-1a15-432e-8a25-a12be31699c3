package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskAuditResultdetailV1;
import com.wzsec.modules.mask.service.dto.MaskAuditResultdetailV1Dto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskAuditResultdetailV1MapperImpl implements MaskAuditResultdetailV1Mapper {

    @Override
    public MaskAuditResultdetailV1 toEntity(MaskAuditResultdetailV1Dto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskAuditResultdetailV1 maskAuditResultdetailV1 = new MaskAuditResultdetailV1();

        maskAuditResultdetailV1.setId( dto.getId() );
        maskAuditResultdetailV1.setTaskname( dto.getTaskname() );
        maskAuditResultdetailV1.setTablename( dto.getTablename() );
        maskAuditResultdetailV1.setFilename( dto.getFilename() );
        maskAuditResultdetailV1.setField( dto.getField() );
        maskAuditResultdetailV1.setAlgorithm( dto.getAlgorithm() );
        maskAuditResultdetailV1.setLinenumber( dto.getLinenumber() );
        maskAuditResultdetailV1.setIssafesame( dto.getIssafesame() );
        maskAuditResultdetailV1.setRisk( dto.getRisk() );
        maskAuditResultdetailV1.setExample( dto.getExample() );
        maskAuditResultdetailV1.setChecktime( dto.getChecktime() );
        maskAuditResultdetailV1.setSubmitter( dto.getSubmitter() );
        maskAuditResultdetailV1.setSparefield1( dto.getSparefield1() );
        maskAuditResultdetailV1.setSparefield2( dto.getSparefield2() );
        maskAuditResultdetailV1.setSparefield3( dto.getSparefield3() );
        maskAuditResultdetailV1.setSparefield4( dto.getSparefield4() );

        return maskAuditResultdetailV1;
    }

    @Override
    public MaskAuditResultdetailV1Dto toDto(MaskAuditResultdetailV1 entity) {
        if ( entity == null ) {
            return null;
        }

        MaskAuditResultdetailV1Dto maskAuditResultdetailV1Dto = new MaskAuditResultdetailV1Dto();

        maskAuditResultdetailV1Dto.setId( entity.getId() );
        maskAuditResultdetailV1Dto.setTaskname( entity.getTaskname() );
        maskAuditResultdetailV1Dto.setTablename( entity.getTablename() );
        maskAuditResultdetailV1Dto.setFilename( entity.getFilename() );
        maskAuditResultdetailV1Dto.setField( entity.getField() );
        maskAuditResultdetailV1Dto.setAlgorithm( entity.getAlgorithm() );
        maskAuditResultdetailV1Dto.setLinenumber( entity.getLinenumber() );
        maskAuditResultdetailV1Dto.setIssafesame( entity.getIssafesame() );
        maskAuditResultdetailV1Dto.setRisk( entity.getRisk() );
        maskAuditResultdetailV1Dto.setExample( entity.getExample() );
        maskAuditResultdetailV1Dto.setChecktime( entity.getChecktime() );
        maskAuditResultdetailV1Dto.setSubmitter( entity.getSubmitter() );
        maskAuditResultdetailV1Dto.setSparefield1( entity.getSparefield1() );
        maskAuditResultdetailV1Dto.setSparefield2( entity.getSparefield2() );
        maskAuditResultdetailV1Dto.setSparefield3( entity.getSparefield3() );
        maskAuditResultdetailV1Dto.setSparefield4( entity.getSparefield4() );

        return maskAuditResultdetailV1Dto;
    }

    @Override
    public List<MaskAuditResultdetailV1> toEntity(List<MaskAuditResultdetailV1Dto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskAuditResultdetailV1> list = new ArrayList<MaskAuditResultdetailV1>( dtoList.size() );
        for ( MaskAuditResultdetailV1Dto maskAuditResultdetailV1Dto : dtoList ) {
            list.add( toEntity( maskAuditResultdetailV1Dto ) );
        }

        return list;
    }

    @Override
    public List<MaskAuditResultdetailV1Dto> toDto(List<MaskAuditResultdetailV1> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskAuditResultdetailV1Dto> list = new ArrayList<MaskAuditResultdetailV1Dto>( entityList.size() );
        for ( MaskAuditResultdetailV1 maskAuditResultdetailV1 : entityList ) {
            list.add( toDto( maskAuditResultdetailV1 ) );
        }

        return list;
    }
}
