package com.wzsec.modules.mask.service.mapper;

import com.wzsec.modules.mask.domain.MaskKanonymizationresult;
import com.wzsec.modules.mask.service.dto.MaskKanonymizationresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T16:04:24+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskKanonymizationresultMapperImpl implements MaskKanonymizationresultMapper {

    @Override
    public MaskKanonymizationresult toEntity(MaskKanonymizationresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskKanonymizationresult maskKanonymizationresult = new MaskKanonymizationresult();

        maskKanonymizationresult.setId( dto.getId() );
        maskKanonymizationresult.setTaskname( dto.getTaskname() );
        maskKanonymizationresult.setDbname( dto.getDbname() );
        maskKanonymizationresult.setTabname( dto.getTabname() );
        maskKanonymizationresult.setMaskline( dto.getMaskline() );
        maskKanonymizationresult.setTotalline( dto.getTotalline() );
        maskKanonymizationresult.setTaskstatus( dto.getTaskstatus() );
        maskKanonymizationresult.setTaskresultestimate( dto.getTaskresultestimate() );
        maskKanonymizationresult.setOutputdirectory( dto.getOutputdirectory() );
        maskKanonymizationresult.setBeforemaskdata( dto.getBeforemaskdata() );
        maskKanonymizationresult.setAftermaskdata( dto.getAftermaskdata() );
        maskKanonymizationresult.setStarttime( dto.getStarttime() );
        maskKanonymizationresult.setEndtime( dto.getEndtime() );
        maskKanonymizationresult.setRemark( dto.getRemark() );
        maskKanonymizationresult.setSparefield1( dto.getSparefield1() );
        maskKanonymizationresult.setSparefield2( dto.getSparefield2() );
        maskKanonymizationresult.setSparefield3( dto.getSparefield3() );
        maskKanonymizationresult.setSparefield4( dto.getSparefield4() );
        maskKanonymizationresult.setSparefield5( dto.getSparefield5() );
        maskKanonymizationresult.setSparefield6( dto.getSparefield6() );

        return maskKanonymizationresult;
    }

    @Override
    public MaskKanonymizationresultDto toDto(MaskKanonymizationresult entity) {
        if ( entity == null ) {
            return null;
        }

        MaskKanonymizationresultDto maskKanonymizationresultDto = new MaskKanonymizationresultDto();

        maskKanonymizationresultDto.setId( entity.getId() );
        maskKanonymizationresultDto.setTaskname( entity.getTaskname() );
        maskKanonymizationresultDto.setDbname( entity.getDbname() );
        maskKanonymizationresultDto.setTabname( entity.getTabname() );
        maskKanonymizationresultDto.setMaskline( entity.getMaskline() );
        maskKanonymizationresultDto.setTotalline( entity.getTotalline() );
        maskKanonymizationresultDto.setTaskstatus( entity.getTaskstatus() );
        maskKanonymizationresultDto.setTaskresultestimate( entity.getTaskresultestimate() );
        maskKanonymizationresultDto.setOutputdirectory( entity.getOutputdirectory() );
        maskKanonymizationresultDto.setBeforemaskdata( entity.getBeforemaskdata() );
        maskKanonymizationresultDto.setAftermaskdata( entity.getAftermaskdata() );
        maskKanonymizationresultDto.setStarttime( entity.getStarttime() );
        maskKanonymizationresultDto.setEndtime( entity.getEndtime() );
        maskKanonymizationresultDto.setRemark( entity.getRemark() );
        maskKanonymizationresultDto.setSparefield1( entity.getSparefield1() );
        maskKanonymizationresultDto.setSparefield2( entity.getSparefield2() );
        maskKanonymizationresultDto.setSparefield3( entity.getSparefield3() );
        maskKanonymizationresultDto.setSparefield4( entity.getSparefield4() );
        maskKanonymizationresultDto.setSparefield5( entity.getSparefield5() );
        maskKanonymizationresultDto.setSparefield6( entity.getSparefield6() );

        return maskKanonymizationresultDto;
    }

    @Override
    public List<MaskKanonymizationresult> toEntity(List<MaskKanonymizationresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskKanonymizationresult> list = new ArrayList<MaskKanonymizationresult>( dtoList.size() );
        for ( MaskKanonymizationresultDto maskKanonymizationresultDto : dtoList ) {
            list.add( toEntity( maskKanonymizationresultDto ) );
        }

        return list;
    }

    @Override
    public List<MaskKanonymizationresultDto> toDto(List<MaskKanonymizationresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskKanonymizationresultDto> list = new ArrayList<MaskKanonymizationresultDto>( entityList.size() );
        for ( MaskKanonymizationresult maskKanonymizationresult : entityList ) {
            list.add( toDto( maskKanonymizationresult ) );
        }

        return list;
    }
}
